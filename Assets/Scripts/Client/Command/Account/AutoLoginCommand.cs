using System;
using System.Threading;
using Client.Event.UserEvent;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient.Event.UserEvent;
using Newtonsoft.Json;
using QFramework;
using TFG.UI;
using TFGShare.Protocol;
using UnityEngine;

namespace GameClient.Command
{
    public class AutoLoginResult
    {
        public bool Success = false;
        public string MsgKey;
        public LoginSuccessData LoginSuccessData;
        public string ErrorMsg = String.Empty;
    }

    public class AutoLoginCommand : AbstractCommand<AutoLoginResult>
    {
        protected string AutoLoginToken => LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().AutoLoginToken;
        protected string ClientVersion
        {
            get
            {
                switch (LaunchMainArch.Interface.GetModel<ClientApplicationModel>().Platform)
                {
                    case ClientPlatform.None:
                    case ClientPlatform.Windows:
                    case ClientPlatform.Mac:
                    case ClientPlatform.WebGl:
                    case ClientPlatform.Editor:
                    case ClientPlatform.Linux:
                    case ClientPlatform.Other:
                        return string.Concat("pc", " ",LaunchMainArch.Interface.GetModel<ClientApplicationModel>().AppVersion);
                    case ClientPlatform.Ios:
                    case ClientPlatform.Android:
                        return string.Concat("android", " ",LaunchMainArch.Interface.GetModel<ClientApplicationModel>().AppVersion);
                    default:
                        return string.Concat("pc"," ", LaunchMainArch.Interface.GetModel<ClientApplicationModel>().AppVersion);
                }
            }
        }

        protected string GetRequestJson()
        {
            AutoLoginRequest registerRequest = new AutoLoginRequest()
            {
                AutoLoginToken = AutoLoginToken,
                ClientVersion = ClientVersion
            };
            return registerRequest.ToJson();
        }
        protected ApiSignatureHelper apiSignatureHelper;

        protected override async UniTask<AutoLoginResult> OnExecuteAsync()
        {
            var tcs = new CancellationTokenSource();
            var LocalizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string loading = LocalizationSystem.GetLocalizationContent("Connecting");
            string errormsg = LocalizationSystem.GetLocalizationContent("Error");
            string ok = LocalizationSystem.GetLocalizationContent("OK");
            string requestError = LocalizationSystem.GetLocalizationContent("RequestError");
            apiSignatureHelper = new ApiSignatureHelper(ApiHelper.QA_SECRET_KEY);
            var loadingDialogTask = UIDialogHelper.ShowNormalTips("", loading, 1f, tcs.Token);
            try
            {
                var requestJson = GetRequestJson();
                LogKit.I($"[{GetType().FullName}::OnExecuteAsync] RegisterRequest: {requestJson}");
                var response = await HttpHelper.Create().WithJsonBody(requestJson)
                    .WithPreprocessor(apiSignatureHelper.GetPreprocessor())
                    .PostAsync<BackendApiResponse<LoginSuccessData>>(ApiHelper.QA_GetAutoLoginApi);
                tcs.Cancel();
                await loadingDialogTask;
                //httpError
                if (!response.IsSuccess)
                {
                    this.SendEvent(new LoginFailedEvent(response.ErrorMessage));
                    LogKit.E(
                        $"[{GetType().FullName}::OnGetVerificationCode] HttpReqError!||" +
                        $"rawResponse:{response.RawResponse}||" +
                        $"ErrorMessage:{response.ErrorMessage}||" +
                        $"StatusCode:{response.StatusCode}||" +
                        $"Data:{response.Data}");
                    UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
                    return new AutoLoginResult()
                    {
                        Success = false,
                        LoginSuccessData = null,
                        ErrorMsg = string.Empty,
                        MsgKey = "RequestError"
                    };
                }
                if (response.Data.Success)
                {
                    return HandleSuccess(response.Data);
                }
                else
                {
                    return HandleFailed(response.Data);
                }
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}] An unexpected error occurred: {e.Message}");
                this.SendEvent(new LoginFailedEvent(e.Message));
                if (!tcs.IsCancellationRequested) tcs.Cancel();
                await loadingDialogTask;
                return new AutoLoginResult()
                {
                    Success = false,LoginSuccessData = null, ErrorMsg = e.Message,MsgKey = "requestError"
                };
            }
            finally
            {
                tcs.Dispose();
            }
        }

        /// <summary>
        /// 处理API调用成功的情况。
        /// </summary>
        private AutoLoginResult HandleSuccess(BackendApiResponse<LoginSuccessData> response)
        {
            LogKit.I($"[{GetType().FullName}::HandleLoginSuccess] 登录成功: {response.Message}");
            PlayerData playerData = new PlayerData
            {
                PlayerId = response.Data.User.ID,
                Username = response.Data.User.Name,
                Role = response.Data.User.Role,
                CharacterId = "",
                LastActiveTime = DateTime.UtcNow,
                AllowedSessionTypes = GameSessionType.OnLanPlay
            };
            this.SendEvent(new LoginSuccessEvent(playerData, false));
            return new AutoLoginResult()
            {
                Success = true,
                LoginSuccessData = response.Data,
                ErrorMsg = string.Empty,
                MsgKey = string.Empty
            };
        }

        /// <summary>
        /// 处理API调用失败的情况。
        /// </summary>
        private AutoLoginResult HandleFailed(BackendApiResponse<LoginSuccessData> response)
        {
            this.SendEvent(new LoginFailedEvent(response.Message));
            if (response.MsgKey.IsNullOrEmpty())
            {
                return new AutoLoginResult()
                {
                    Success = false,
                    LoginSuccessData = null,
                    ErrorMsg = response.Message,
                    MsgKey = "RequestError"
                };
            }
            else
            {
                return new AutoLoginResult
                {
                    Success = false,
                    LoginSuccessData = null,
                    ErrorMsg = response.Message,
                    MsgKey = response.MsgKey
                };
            }
        }
    }
}
