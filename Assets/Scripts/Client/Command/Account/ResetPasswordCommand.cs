using System;
using System.Threading;
using Client.Event.UserEvent;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using QFramework;
using TFG.UI;
using UnityEngine;

namespace GameClient.Command
{
    public class ResetPasswordEmailCommand : ResetPasswordCommand
    {
        private string Email;

        public ResetPasswordEmailCommand(string email,string password) : base(
            password)
        {
            Email = email;
            Account = email;
        }

        public sealed override string Account { get; set; }

        protected override void AfterResetSuccess()
        {
            var model = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            model.LastEmail = Email;
            model.LastPhone = "";
            model.LastCountryCodeIndex = 0;
            model.LoginMethod = LoginMethod.EmailWithPassword;
            model.CheckType = CheckType.Password;
            model.LastAccount = Email;
            model.Account2VerifyTokenMap[model.LastAccount] = "";
        }
    }

    public class ResetPasswordPhoneCommand : ResetPasswordCommand
    {
        private string Phone;
        private int CountryCodeIndex;
        private string CountryCode;

        public ResetPasswordPhoneCommand(int countryCodeIndex, string countryCode, string phone,
            string newPassword) : base(newPassword)
        {
            Phone = phone;
            CountryCode = countryCode;
            CountryCodeIndex = countryCodeIndex;
            Account = string.Concat("+", CountryCode, "-", phone);
        }

        public sealed override string Account { get; set; }

        protected override void AfterResetSuccess()
        {
            var model = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            model.LastEmail = "";
            model.LastPhone = Phone;
            model.LastCountryCodeIndex = CountryCodeIndex;
            model.LoginMethod = LoginMethod.PhoneWithPassword;
            model.CheckType = CheckType.Password;
            model.LastAccount = string.Concat("+",CountryCode, "-", Phone);
            model.Account2VerifyTokenMap[model.LastAccount] = "";
        }
    }

    public abstract class ResetPasswordCommand : AbstractCommand
    {
        public string VerificationCode => LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().CacheVerifyCode;
        public readonly string NewPassword;
        public abstract string Account { get; set; }

        public ResetPasswordCommand(string newPassword)
        {
            this.NewPassword = newPassword;
        }

        protected abstract void AfterResetSuccess();

        protected override void OnExecute()
        {
            base.OnExecute();
            OnResetPassword().Forget();
        }

        private async UniTask OnResetPassword()
        {
            var tcs = new CancellationTokenSource();
            var LocalizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string loading = LocalizationSystem.GetLocalizationContent("Connecting");
            string errormsg = LocalizationSystem.GetLocalizationContent("Error");
            string ok = LocalizationSystem.GetLocalizationContent("OK");
            string requestError = LocalizationSystem.GetLocalizationContent("RequestError");
            var loadingDialog = UIDialogHelper.ShowNormalTips("", loading, 1f, tcs.Token);
            try
            {
                var model = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
                model.Account2VerifyTokenMap.TryGetValue(Account, out var token);
                token = token ?? ""; 
                var request = new ResetPasswordRequest(token, NewPassword, VerificationCode);
                LogKit.I($"[{GetType().FullName}::OnResetPassword] {request.ToJson()}");
                var response = await HttpHelper.Create().WithJsonBody(request.ToJson())
                    .PostAsync<BackendApiResponse<ResetSuccessData>>(ApiHelper.QA_GetResetPasswordApi);
                tcs.Cancel();
                await loadingDialog;
                //Http错误
                if (!response.IsSuccess)
                {
                    this.SendEvent(new ResetPasswordFailedEvent());
                    LogKit.E(
                        $"[{GetType().FullName}::OnGetVerificationCode] HttpReqError!||" +
                        $"rawResponse:{response.RawResponse}||" +
                        $"ErrorMessage:{response.ErrorMessage}||" +
                        $"StatusCode:{response.StatusCode}||" +
                        $"Data:{response.Data}");
                    UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
                    return;
                }
                if (response.Data.Success)
                {
                    HandleSuccess(response.Data);
                }
                else
                {
                    HandleFailed(response.Data);
                }
            }
            //未知错误
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}] An unexpected error occurred during login: {e.Message}");
                this.SendEvent(new ResetPasswordFailedEvent());
                if (!tcs.IsCancellationRequested)
                {
                    tcs.Cancel();
                }
                await loadingDialog;
                UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
            }
            finally
            {
                tcs.Dispose();
            }
        }

        protected void HandleSuccess(BackendApiResponse<ResetSuccessData> apiResponse)
        {
            LogKit.I($"[{GetType().FullName}::HandleSuccess] 密码重置成功");
            this.SendEvent(new ResetPasswordSuccessEvent());
            AfterResetSuccess();
        }

        protected void HandleFailed(BackendApiResponse<ResetSuccessData> apiResponse)
        {
            this.SendEvent(new ResetPasswordFailedEvent());
            var localizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string error = localizationSystem.GetLocalizationContent("Error");
            string ok = localizationSystem.GetLocalizationContent("OK");
            var erroKey = apiResponse.MsgKey.IsNullOrEmpty()? "RequestError":apiResponse.MsgKey;
            var errorMsg = localizationSystem.GetLocalizationContent(erroKey);
            UIDialogHelper.ShowNormalStyle(error, errorMsg, true, false,
                confirmText: ok);
        }
    }
}
