using System;
using System.Threading;
using Client.Event.UserEvent;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using QFramework;
using TFG.UI;
using UnityEngine;

namespace GameClient.Command
{
    public abstract class GetVerificationCodeCommand : AbstractCommand
    {
        public abstract string Account { get; set; }
        public abstract string Phone { get; set; }
        public abstract string Email { get; set; }
        public abstract int CountryCodeIndex { get; set; }
        public abstract string Type { get; set; }
        public string VerifyToken { get; set; }

        protected override void OnExecute()
        {
            base.OnExecute();
            OnGetVerificationCode().Forget();
        }

        protected abstract void AfterGetVerificationSuccess();

        protected virtual async UniTask OnGetVerificationCode()
        {
            CancellationTokenSource tcs = new CancellationTokenSource();
            var LocalizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string loading = LocalizationSystem.GetLocalizationContent("Connecting");
            string errormsg = LocalizationSystem.GetLocalizationContent("Error");
            string ok = LocalizationSystem.GetLocalizationContent("OK");
            string requestError = LocalizationSystem.GetLocalizationContent("RequestError");
            var loadingDialog = UIDialogHelper.ShowNormalTips("", loading, 1f, tcs.Token);
            // 如果你真的不需要等待，可以像这样忽略返回值
            // _ = UIDialogHelper.ShowNormalTips("", "Loading", 1f, tcs.Token);
            try
            {
                var request = new GetVerificationCodeRequest(Account, Type);
                LogKit.I($"[{GetType().FullName}::OnGetVerificationCode] {request.ToJson()}");
                var response = await HttpHelper.Create().WithJsonBody(request.ToJson())
                    .PostAsync<BackendApiResponse<GetVerificationCodeSuccessData>>(ApiHelper.QA_GetSendVerificationCodeApi);
                tcs.Cancel();
                await loadingDialog;
                //Http错误
                if (!response.IsSuccess)
                {
                    this.SendEvent(new GetVerificationFailedEvent());
                    LogKit.E(
                        $"[{GetType().FullName}::OnGetVerificationCode] HttpReqError!||" +
                        $"rawResponse:{response.RawResponse}||" +
                        $"ErrorMessage:{response.ErrorMessage}||" +
                        $"StatusCode:{response.StatusCode}||" +
                        $"Data:{response.Data}");
                    UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
                    return;
                }
                if (response.Data.Success)
                {
                    HandleSuccess(response.Data);
                }
                else
                {
                    HandleFailed(response);
                }
            }
            //未知错误
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}] An unexpected error occurred during login: {e.Message}{e.ToString()}");
                this.SendEvent(new GetVerificationFailedEvent());
                if (!tcs.IsCancellationRequested)
                {
                    tcs.Cancel();
                }
                await loadingDialog;
                UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
            }
            finally
            {
                tcs.Dispose();
            }
        }

        protected virtual void HandleSuccess(
            BackendApiResponse<GetVerificationCodeSuccessData> apiResponse)
        {
            var LocalizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            LogKit.I($"[{GetType().FullName}::HandleLoginSuccess] 获取验证码成功: {apiResponse.Message}");
            this.SendEvent(new GetVerificationSuccessEvent());
            var msg = LocalizationSystem.GetLocalizationContent("DialogTestSDK");
            var ok = LocalizationSystem.GetLocalizationContent("OK");
            UIDialogHelper.ShowNormalStyle("", msg,true,false,confirmText:ok);
            VerifyToken = apiResponse.Data.VerifyToken;
            AfterGetVerificationSuccess();
        }

        protected virtual void HandleFailed(HttpResponse<BackendApiResponse<GetVerificationCodeSuccessData>> httpResponse)
        {
            this.SendEvent(new GetVerificationFailedEvent());
            var localizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string errorTitle = localizationSystem.GetLocalizationContent("Error");
            string backendMessage = httpResponse.Data.Message;
            var erroKey = httpResponse.Data.MsgKey.IsNullOrEmpty()? "RequestError":httpResponse.Data.MsgKey;
            var errorMsg = localizationSystem.GetLocalizationContent(erroKey);
            LogKit.E($"[{GetType().FullName}::HandleGetVerificationCodeFailed] Business logic error: {backendMessage} (MsgKey: {httpResponse.Data.MsgKey})");
            string ok = localizationSystem.GetLocalizationContent("OK");
            UIDialogHelper.ShowNormalStyle(errorTitle, errorMsg, true, false, confirmText: ok);
        }
        
    }

    public class GetVerificationCodeLoginCommand : GetVerificationCodeCommand
    {
        public sealed override string Account { get; set; }
        public override string Phone { get; set; }
        public override string Email { get; set; }
        public override int CountryCodeIndex { get; set; }
        public override string Type { get; set; } = "Login";

        public GetVerificationCodeLoginCommand(string account, string email, string phone,
            int countryCodeIndex)
        {
            Account = account;
            Email = email;
            Phone = phone;
            CountryCodeIndex = countryCodeIndex;
        }
        protected override void AfterGetVerificationSuccess()
        {
            //保存验证用的token
            LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().Account2HasLoginVerify[Account] = true;
            LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().Account2VerifyTokenMap[Account] = VerifyToken;
            switch (LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().AccountType)
            {
                case AccountType.PhoneNumber:
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastEmail = "";
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastPhone = Phone;
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex = CountryCodeIndex;
                    break;
                case AccountType.Email:
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastEmail = Email;
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastPhone = "";
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex = 0;
                    break;
            }
        }
    }

    public class GetVerificationCodeRegisterCommand : GetVerificationCodeCommand
    {
        public sealed override string Account { get; set; }
        public override string Phone { get; set; }
        public override string Email { get; set; }
        public override int CountryCodeIndex { get; set; }
        public override string Type { get; set; } = "Register";

        public GetVerificationCodeRegisterCommand(string account, string email, string phone,
            int countryCodeIndex)
        {
            Account = account;
            Email = email;
            Phone = phone;
            CountryCodeIndex = countryCodeIndex;
        }
        protected override void AfterGetVerificationSuccess()
        {
            //保存验证用的token
            LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().Account2VerifyTokenMap[Account] = VerifyToken;
            switch (LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().AccountType)
            {
                case AccountType.PhoneNumber:
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastEmail = "";
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastPhone = Phone;
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex = CountryCodeIndex;
                    break;
                case AccountType.Email:
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastEmail = Email;
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastPhone = "";
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex = 0;
                    break;
            }
        }
    }

    public class GetVerificationCodeResetPasswordCommand : GetVerificationCodeCommand
    {
        public sealed override string Account { get; set; }
        public override string Phone { get; set; }
        public override string Email { get; set; }
        public override int CountryCodeIndex { get; set; }
        public override string Type { get; set; } = "ResetPassword";

        public GetVerificationCodeResetPasswordCommand(string account, string email, string phone,
            int countryCodeIndex)
        {
            Account = account;
            Email = email;
            Phone = phone;
            CountryCodeIndex = countryCodeIndex;
        }
        protected override void AfterGetVerificationSuccess()
        {
            //保存验证用的token
            LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().Account2VerifyTokenMap[Account] = VerifyToken;
            switch (LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().AccountType)
            {
                case AccountType.PhoneNumber:
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastEmail = "";
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastPhone = Phone;
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex = CountryCodeIndex;
                    break;
                case AccountType.Email:
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastEmail = Email;
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastPhone = "";
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex = 0;
                    break;
            }
        }
    }
    public class GetVerificationCodeBindAccountCommand : GetVerificationCodeCommand
    {
        public sealed override string Account { get; set; }
        public override string Phone { get; set; }
        public override string Email { get; set; }
        public override int CountryCodeIndex { get; set; }
        public override string Type { get; set; } = "BindAccount";

        public GetVerificationCodeBindAccountCommand(string account, string email, string phone,
            int countryCodeIndex)
        {
            Account = account;
            Email = email;
            Phone = phone;
            CountryCodeIndex = countryCodeIndex;
        }
        protected override void AfterGetVerificationSuccess()
        {
            //保存验证用的token
            LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().Account2VerifyTokenMap[Account] = VerifyToken;
            switch (LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().AccountType)
            {
                case AccountType.PhoneNumber:
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastEmail = "";
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastPhone = Phone;
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex = CountryCodeIndex;
                    break;
                case AccountType.Email:
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastEmail = Email;
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastPhone = "";
                    LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex = 0;
                    break;
            }
        }
    }

    public class VerifyVerificationCodeResult
    {
        public bool IsSuccess;
        public string MsgKey;
        public string ErrorMsg = String.Empty;
    }

    public class VerifyVerificationCodeCommand : AbstractCommand<VerifyVerificationCodeResult>
    {
        public readonly string VerificationToken;
        public readonly string VerificationCode;

        public VerifyVerificationCodeCommand(string verificationToken,string verificationCode)
        {
            VerificationToken = verificationToken;
            VerificationCode = verificationCode;
        }

        protected override async UniTask<VerifyVerificationCodeResult> OnExecuteAsync()
        {
            var tcs = new CancellationTokenSource();
            var LocalizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string loading = LocalizationSystem.GetLocalizationContent("Connecting");
            string errormsg = LocalizationSystem.GetLocalizationContent("Error");
            string ok = LocalizationSystem.GetLocalizationContent("OK");
            string requestError = LocalizationSystem.GetLocalizationContent("RequestError");
            var loadingDialog = UIDialogHelper.ShowNormalTips("", loading, 1f, tcs.Token);
            try
            {
                var requestJson = new VerifyVerificationCodeRequest
                {
                    VerifyToken = VerificationToken, VerifyCode = VerificationCode
                }.ToJson();
                LogKit.I($"[{GetType().FullName}::OnExecuteAsync] VerifyVerificationCodeRequest: {requestJson}");
                var response = await HttpHelper.Create().WithJsonBody(requestJson)
                    .PostAsync<BackendApiResponse<VerifyVerificationCodeSuccessData>>(ApiHelper.QA_GetCheckVerificationCodeApi);
                tcs.Cancel();
                await loadingDialog;
                //Http错误
                if (!response.IsSuccess)
                {
                    this.SendEvent(new VerifyVerificationCodeFailedEvent());
                    LogKit.E(
                        $"[{GetType().FullName}::OnGetVerificationCode] HttpReqError!||" +
                        $"rawResponse:{response.RawResponse}||" +
                        $"ErrorMessage:{response.ErrorMessage}||" +
                        $"StatusCode:{response.StatusCode}||" +
                        $"Data:{response.Data}");
                    UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
                    return new VerifyVerificationCodeResult
                    {
                        IsSuccess = false,
                        MsgKey = "requestError",
                        ErrorMsg = string.Empty
                    };
                }
                if (response.Data.Success)
                {
                    return Handleuccess(response.Data);
                }
                else
                {
                    return HandleFailed(response);
                }
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}] An unexpected error occurred: {e.Message}");
                this.SendEvent(new VerifyVerificationCodeFailedEvent());
                if (!tcs.IsCancellationRequested) tcs.Cancel();
                await loadingDialog;
                return new VerifyVerificationCodeResult
                {
                    IsSuccess = false,
                    MsgKey = "requestError",
                    ErrorMsg = string.Empty
                };
            }
            finally
            {
                tcs.Dispose();
            }
        }

        private VerifyVerificationCodeResult HandleFailed(HttpResponse<BackendApiResponse<VerifyVerificationCodeSuccessData>> response)
        {
            this.SendEvent(new VerifyVerificationCodeFailedEvent());
            VerifyVerificationCodeResult res = new VerifyVerificationCodeResult
            {
                IsSuccess = false,
                ErrorMsg = response.Data.Message,
                MsgKey = response.Data.MsgKey
            };
            if (response.Data.MsgKey.IsNullOrEmpty())
            {
                res.MsgKey = "RequestError";
            }
            return res;
        }

        private VerifyVerificationCodeResult Handleuccess(BackendApiResponse<VerifyVerificationCodeSuccessData> responseData)
        {
            LogKit.I($"[{GetType().FullName}::HandleRegisterSuccess] 验证码校验成功: {responseData.Message}");
            this.SendEvent(new VerifyVerificationCodeSuccessEvent());
            VerifyVerificationCodeResult res = new VerifyVerificationCodeResult
            {
                IsSuccess = true,
                MsgKey = string.Empty,
                ErrorMsg = string.Empty
            };
            return res;
        }
    }
}
