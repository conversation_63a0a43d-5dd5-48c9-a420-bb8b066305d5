using System;
using System.Threading;
using Client.Event.UserEvent;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using QFramework;
using TFG.UI;
using TFGShare.Protocol;
using UnityEngine;

namespace GameClient.Command
{
    public class RegisterResult
    {
        public bool Success = false;
        public string MsgKey;
        public RegisterSuccessData RegisterSuccessData;
        public string ErrorMsg = String.Empty;
    }

    public abstract class AbstractAsyncRegisterCommand : AbstractCommand<RegisterResult>
    {
        protected string VerifyToken;
        protected abstract string Account { get; set; }
        protected abstract string Password { get; set; }

        protected string VerifyCode => LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().CacheVerifyCode;
        protected string ClientVersion
        {
            get
            {
                switch (LaunchMainArch.Interface.GetModel<ClientApplicationModel>().Platform)
                {
                    case ClientPlatform.None:
                    case ClientPlatform.Windows:
                    case ClientPlatform.Mac:
                    case ClientPlatform.WebGl:
                    case ClientPlatform.Editor:
                    case ClientPlatform.Linux:
                    case ClientPlatform.Other:
                        return string.Concat("pc", " ",LaunchMainArch.Interface.GetModel<ClientApplicationModel>().AppVersion);
                    case ClientPlatform.Ios:
                    case ClientPlatform.Android:
                        return string.Concat("android", " ",LaunchMainArch.Interface.GetModel<ClientApplicationModel>().AppVersion);
                    default:
                        return string.Concat("pc"," ", LaunchMainArch.Interface.GetModel<ClientApplicationModel>().AppVersion);
                }
            }
        }

        protected string GetRequestJson()
        {
            var model = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            model.Account2VerifyTokenMap.TryGetValue(Account, out var token);
            token = token ?? ""; 
            RegisterRequest registerRequest = new RegisterRequest
            {
                VerifyToken = token,
                Password = Password,
                VerifyCode = VerifyCode,
                ClientVersion = ClientVersion
            };
            return registerRequest.ToJson();
        }

        protected abstract void AfterRegisterSuccess();
        protected ApiSignatureHelper apiSignatureHelper;

        protected override async UniTask<RegisterResult> OnExecuteAsync()
        {
            var tcs = new CancellationTokenSource();
            var LocalizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string loading = LocalizationSystem.GetLocalizationContent("Connecting");
            string errormsg = LocalizationSystem.GetLocalizationContent("Error");
            string ok = LocalizationSystem.GetLocalizationContent("OK");
            string requestError = LocalizationSystem.GetLocalizationContent("RequestError");
            apiSignatureHelper = new ApiSignatureHelper(ApiHelper.QA_SECRET_KEY);
            var loadingDialogTask = UIDialogHelper.ShowNormalTips("", loading, 1f, tcs.Token);
            try
            {
                var requestJson = GetRequestJson();
                LogKit.I($"[{GetType().FullName}::OnExecuteAsync] RegisterRequest: {requestJson}");
                var response = await HttpHelper.Create().WithJsonBody(requestJson)
                    .WithPreprocessor(apiSignatureHelper.GetPreprocessor())
                    .PostAsync<BackendApiResponse<RegisterSuccessData>>(ApiHelper.QA_GetRegisterApi);
                tcs.Cancel();
                await loadingDialogTask;
                //httpError
                if (!response.IsSuccess)
                {
                    this.SendEvent(new RegisterFailedEvent());
                    LogKit.E(
                        $"[{GetType().FullName}::OnGetVerificationCode] HttpReqError!||" +
                        $"rawResponse:{response.RawResponse}||" +
                        $"ErrorMessage:{response.ErrorMessage}||" +
                        $"StatusCode:{response.StatusCode}||" +
                        $"Data:{response.Data}");
                    UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
                    return new RegisterResult
                    {
                        Success = false,
                        RegisterSuccessData = null,
                        ErrorMsg = string.Empty,
                        MsgKey = "RequestError"
                    };
                }
                if (response.Data.Success)
                {
                    return HandleRegisterSuccess(response.Data);
                }
                else
                {
                    return HandleRegisterFailed(response.Data);
                }
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}] An unexpected error occurred: {e.Message}");
                this.SendEvent(new RegisterFailedEvent());
                if (!tcs.IsCancellationRequested) tcs.Cancel();
                await loadingDialogTask;
                return new RegisterResult()
                {
                    Success = false,RegisterSuccessData = null, ErrorMsg = e.Message,MsgKey = "requestError"
                };
            }
            finally
            {
                tcs.Dispose();
            }
        }

        /// <summary>
        /// 处理API调用成功的情况。
        /// </summary>
        private RegisterResult HandleRegisterSuccess(BackendApiResponse<RegisterSuccessData> apiResponse)
        {
            LogKit.I($"[{GetType().FullName}::HandleRegisterSuccess] 注册成功: {apiResponse.Message}");
            this.SendEvent(new RegisterSuccessEvent());
            AfterRegisterSuccess();
            var res = new RegisterResult
            {
                Success = true,
                MsgKey = string.Empty,
                RegisterSuccessData = apiResponse.Data,
                ErrorMsg = string.Empty
            };
            return res;
        }

        /// <summary>
        /// 处理API调用失败的情况。
        /// </summary>
        private RegisterResult HandleRegisterFailed(BackendApiResponse<RegisterSuccessData> apiResponse)
        {
            this.SendEvent(new RegisterFailedEvent());
            if (apiResponse.MsgKey.IsNullOrEmpty())
            {
                return new RegisterResult
                {
                    Success = false,
                    RegisterSuccessData = null,
                    ErrorMsg = apiResponse.Message,
                    MsgKey = "RequestError"
                };
            }
            else
            {
                return new RegisterResult
                {
                    Success = false,
                    RegisterSuccessData = null,
                    ErrorMsg = apiResponse.Message,
                    MsgKey = apiResponse.MsgKey
                };
            }
        }
    }

    /// <summary>
    /// 手机号异步注册命令的具体实现。
    /// </summary>
    public class PhoneRegisterWithNoPasswordAsyncCommand : AbstractAsyncRegisterCommand
    {
        private readonly int _countryCodeIndex;
        private readonly string _countryCode;
        private readonly string _phone;
        protected sealed override string Account { get; set; }
        protected override string Password { get; set; } = "";

        /// <summary>
        /// 构造函数，用于从调用方接收必要的注册信息。
        /// </summary>
        public PhoneRegisterWithNoPasswordAsyncCommand(int countryCodeIndex, string countryCode, string phone)
        {
            _countryCodeIndex = countryCodeIndex;
            _countryCode = countryCode;
            _phone = phone;
            Account = string.Concat("+", countryCode, "-", phone);
        }

        protected override void AfterRegisterSuccess()
        {
            // 获取账户偏好设置模型
            var prefs = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            prefs.LastEmail = "";
            prefs.LastPhone = _phone;
            prefs.LastCountryCodeIndex = _countryCodeIndex;
            prefs.LastAccount = string.Concat("+",_countryCode, "-", _phone);
            prefs.Account2VerifyTokenMap[prefs.LastAccount] = "";
            prefs.LoginMethod = LoginMethod.PhoneWithVerificationCode;
            prefs.CheckType = CheckType.Verification;
            prefs.AccountType = AccountType.PhoneNumber;
            prefs.IsAutoLogin = false;
        }
    }
    /// <summary>
    /// 手机号异步注册命令的具体实现。
    /// </summary>
    public class PhoneRegisterAsyncCommand : AbstractAsyncRegisterCommand
    {
        private readonly int _countryCodeIndex;
        private readonly string _countryCode;
        private readonly string _phone;
        protected sealed override string Account { get; set; }
        protected sealed override string Password { get; set; }

        /// <summary>
        /// 构造函数，用于从调用方接收必要的注册信息。
        /// </summary>
        public PhoneRegisterAsyncCommand(int countryCodeIndex, string countryCode,string phone, string password)
        {
            _countryCodeIndex = countryCodeIndex;
            _countryCode = countryCode;
            _phone = phone;
            Password = password;
            Account = string.Concat("+", countryCode, "-", phone);
        }

        protected override void AfterRegisterSuccess()
        {
            // 获取账户偏好设置模型
            var prefs = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            prefs.LastEmail = "";
            prefs.LastPhone = _phone;
            prefs.LastCountryCodeIndex = _countryCodeIndex;
            prefs.LastAccount = string.Concat("+",_countryCode, "-", _phone);
            prefs.Account2VerifyTokenMap[prefs.LastAccount] = "";
            prefs.LoginMethod = LoginMethod.PhoneWithPassword;
            prefs.CheckType = CheckType.Password;
            prefs.AccountType = AccountType.PhoneNumber;
        }
    }
    /// <summary>
    /// 邮箱异步注册命令的具体实现。
    /// </summary>
    public class EmailRegisterAsyncCommand : AbstractAsyncRegisterCommand
    {
        private readonly string _email;
        protected sealed override string Account { get; set; }
        protected sealed override string Password { get; set; }

        /// <summary>
        /// 构造函数，用于从调用方接收必要的注册信息。
        /// </summary>
        public EmailRegisterAsyncCommand(string email,string password)
        {
            _email = email;
            Password = password;
            Account = email;
        }

        protected override void AfterRegisterSuccess()
        {
            // 获取账户偏好设置模型
            var prefs = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            prefs.LastEmail = _email;
            prefs.LastAccount = _email;
            prefs.Account2VerifyTokenMap[prefs.LastAccount] = "";
            prefs.LoginMethod = LoginMethod.EmailWithPassword;
            prefs.CheckType = CheckType.Password;
            prefs.AccountType = AccountType.Email;
        }
    }
    
}
