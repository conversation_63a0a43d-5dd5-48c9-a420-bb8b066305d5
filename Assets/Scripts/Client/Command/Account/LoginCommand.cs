using System;
using System.Threading;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient.Event.UserEvent;
using Newtonsoft.Json;
using QFramework;
using TFG.UI;
using TFGShare.Protocol;
using UnityEngine;

namespace GameClient.Command
{
    /// <summary>
    /// 抽象登录命令
    /// 请业务实现具体的登录命令，比如邮箱密码登录、手机验证码登录、手机密码登录等
    /// </summary>
    public abstract class LoginCommand : AbstractCommand
    {
        public string Account { get; set; }
        public string Password { get; set; }
        public string VerifyCode => LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().CacheVerifyCode;
        public abstract string LoginType { get;}
        public bool AutoLogin { get; set; }

        public LoginCommand()
        {
        }

        protected string ClientVersion
        {
            get
            {
                switch (LaunchMainArch.Interface.GetModel<ClientApplicationModel>().Platform)
                {
                    case ClientPlatform.None:
                    case ClientPlatform.Windows:
                    case ClientPlatform.Mac:
                    case ClientPlatform.WebGl:
                    case ClientPlatform.Editor:
                    case ClientPlatform.Linux:
                    case ClientPlatform.Other:
                        return string.Concat("pc", " ",LaunchMainArch.Interface.GetModel<ClientApplicationModel>().AppVersion);
                    case ClientPlatform.Ios:
                    case ClientPlatform.Android:
                        return string.Concat("android", " ",LaunchMainArch.Interface.GetModel<ClientApplicationModel>().AppVersion);
                    default:
                        return string.Concat("pc"," ", LaunchMainArch.Interface.GetModel<ClientApplicationModel>().AppVersion);
                }
            }
        }
        protected ApiSignatureHelper apiSignatureHelper;

        protected override void OnExecute()
        {
            OnLogin().Forget();
        }

        protected abstract void AfterLoginSuccess();

        protected virtual async UniTask OnLogin()
        {
             var tcs = new CancellationTokenSource();
            var LocalizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string loading = LocalizationSystem.GetLocalizationContent("Connecting");
            string errormsg = LocalizationSystem.GetLocalizationContent("Error");
            string ok = LocalizationSystem.GetLocalizationContent("OK");
            string requestError = LocalizationSystem.GetLocalizationContent("RequestError");
            apiSignatureHelper = new ApiSignatureHelper(ApiHelper.QA_SECRET_KEY);
            var loadingDialogTask = UIDialogHelper.ShowNormalTips("", loading, 1f, tcs.Token);
            try
            {
                bool hasVerifycode = string.Equals(LoginType, "code");
                string verifycode = hasVerifycode ? VerifyCode : "";
                var requestJson = new LoginRequest
                {
                    Account = this.Account,
                    Password = this.Password,
                    VerifyCode = verifycode,
                    AutoLogin = this.AutoLogin,
                    ClientVersion = this.ClientVersion,
                    LoginType = this.LoginType
                }.ToJson();
                LogKit.I($"[{GetType().FullName}::OnExecuteAsync] RegisterRequest: {requestJson}");
                var response = await HttpHelper.Create().WithJsonBody(requestJson)
                    .WithPreprocessor(apiSignatureHelper.GetPreprocessor())
                    .PostAsync<BackendApiResponse<LoginSuccessData>>(ApiHelper.QA_GetLoginApi);
                tcs.Cancel();
                await loadingDialogTask;
                //httpError
                if (!response.IsSuccess)
                {
                    this.SendEvent(new LoginFailedEvent(response.ErrorMessage));
                    LogKit.E(
                        $"[{GetType().FullName}::OnGetVerificationCode] HttpReqError!||" +
                        $"rawResponse:{response.RawResponse}||" +
                        $"ErrorMessage:{response.ErrorMessage}||" +
                        $"StatusCode:{response.StatusCode}||" +
                        $"Data:{response.Data}");
                    UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
                }
                if (response.Data.Success)
                {
                    HandleSuccess(response.Data);
                }
                else
                {
                    HandleFailed(response.Data);
                }
            }
            //未知错误
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}] An unexpected error occurred during login: {e.Message}");
                this.SendEvent(new LoginFailedEvent($"An unexpected error occurred during login: {e.Message}"));
                if (!tcs.IsCancellationRequested)
                {
                    tcs.Cancel();
                }
                await loadingDialogTask;
                UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
            }
            finally
            {
                tcs.Dispose();
            }
        }

        protected virtual void HandleSuccess(BackendApiResponse<LoginSuccessData> apiResponse)
        {
            LogKit.I($"[{GetType().FullName}::HandleLoginSuccess] 登录成功: {apiResponse.Message}");
            PlayerData playerData = new PlayerData
            {
                PlayerId = apiResponse.Data.User.ID,
                Username = apiResponse.Data.User.Name,
                Role = apiResponse.Data.User.Role,
                CharacterId = "",
                LastActiveTime = DateTime.UtcNow,
                AllowedSessionTypes = GameSessionType.OnLanPlay
            };
            this.SendEvent(new LoginSuccessEvent(playerData, false));
            //保存登录预设
            AfterLoginSuccess();
            if (AutoLogin)
            {
                LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().AutoLoginToken = apiResponse.Data.PersistentToken;
            }
            else
            {
                LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().AutoLoginToken = "";
            }
        }

        protected virtual void HandleFailed(BackendApiResponse<LoginSuccessData> apiResponse)
        {
            this.SendEvent(new LoginFailedEvent(apiResponse.Message));
            var localizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string error = localizationSystem.GetLocalizationContent("Error");
            string ok = localizationSystem.GetLocalizationContent("OK");
            var erroKey = apiResponse.MsgKey.IsNullOrEmpty()? "RequestError":apiResponse.MsgKey;
            var errorMsg = localizationSystem.GetLocalizationContent(erroKey);
            UIDialogHelper.ShowNormalStyle(error, errorMsg, true, false,
                confirmText: ok);
        }
    }

    // 仅用于GMDebug调试，Release请删除
    public class GMFastLoginCommand : LoginCommand
    {
        
        public readonly string Email;

        public GMFastLoginCommand()
        {
            Email = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().FastLoginAccount;
            Password = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().FastLoginPassword;
            Account = Email;
            AutoLogin = false;
        }

        public override string LoginType { get; } = "password";

        protected override void AfterLoginSuccess()
        {
            var model = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            model.LastEmail = Email;
            model.LastPhone = "";
            model.LastCountryCodeIndex = 0;
            model.LoginMethod = LoginMethod.EmailWithPassword;
            model.AccountType = AccountType.Email;
            model.CheckType = CheckType.Password;
            model.FastLoginAccount = Email;
            model.FastLoginPassword = Password;
            model.LastAccount = Email;
            model.Account2VerifyTokenMap[model.LastAccount] = "";
        }
    }

    public class EmailPasswordLoginCommand : LoginCommand
    {
        public readonly string Email;
        public override string LoginType { get; } = "password";

        public EmailPasswordLoginCommand(string email, string password)
        {
            Email = email;
            Password = password;
            Account = Email;
            AutoLogin = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().IsAutoLogin;
        }

        protected override void AfterLoginSuccess()
        {
            var model = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            model.LastEmail = Email;
            model.LastPhone = "";
            model.LastCountryCodeIndex = 0;
            model.LoginMethod = LoginMethod.EmailWithPassword;
            model.AccountType = AccountType.Email;
            model.CheckType = CheckType.Password;
            model.FastLoginAccount = Email;
            model.FastLoginPassword = Password;
            model.LastAccount = Email;
            model.Account2VerifyTokenMap[model.LastAccount] = "";
        }
    }

    public class PhonePasswordLoginCommand : LoginCommand
    {
        public readonly int CountryCodeIndex;
        public readonly string CountryCode;
        public readonly string Phone;
        public override string LoginType { get; } = "password";
        public string PhoneAccount => string.Concat("+",CountryCode, "-", Phone);

        public PhonePasswordLoginCommand(int countryCodeIndex, string countryCode, string phone, string password)
        {
            CountryCodeIndex = countryCodeIndex;
            CountryCode = countryCode;
            Phone = phone;
            Password = password;
            Account = PhoneAccount;
            AutoLogin = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().IsAutoLogin;
        }

        protected override void AfterLoginSuccess()
        {
            var model = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            model.LastEmail = "";
            model.LastPhone = Phone;
            model.LastCountryCodeIndex = CountryCodeIndex;
            model.LoginMethod = LoginMethod.PhoneWithPassword;
            model.AccountType = AccountType.PhoneNumber;
            model.CheckType = CheckType.Password;
            model.LastAccount = string.Concat("+",CountryCode, "-", Phone);
            model.Account2VerifyTokenMap[model.LastAccount] = "";
        }
    }

    public class PhoneVerificationCodeLoginCommand : LoginCommand
    {
        public readonly int CountryCodeIndex;
        public readonly string CountryCode;
        public readonly string Phone;
        public string PhoneAccount => string.Concat("+",CountryCode, "-", Phone);
        public override string LoginType { get; } = "code";

        public PhoneVerificationCodeLoginCommand(int countryCodeIndex, string countryCode, string phone)
        {
            CountryCodeIndex = countryCodeIndex;
            CountryCode = countryCode;
            Phone = phone;
            Account = PhoneAccount;
            AutoLogin = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().IsAutoLogin;
        }

        protected override void AfterLoginSuccess()
        {
            LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().Account2HasLoginVerify[Account] = false;
            var model = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            model.LastEmail = "";
            model.LastPhone = Phone;
            model.LastCountryCodeIndex = CountryCodeIndex;
            model.LoginMethod = LoginMethod.PhoneWithVerificationCode;
            model.AccountType = AccountType.PhoneNumber;
            model.CheckType = CheckType.Verification;
            model.LastAccount = string.Concat("+",CountryCode, "-", Phone);
            model.Account2VerifyTokenMap[model.LastAccount] = "";
        }
    }
}
