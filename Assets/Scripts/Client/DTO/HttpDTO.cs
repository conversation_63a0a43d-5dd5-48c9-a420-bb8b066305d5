using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace UnityEngine
{

    /// <summary>
    /// 适配后端新标准的通用API响应模型。
    /// 此模型可以同时表示成功和失败的响应。
    /// </summary>
    /// <typeparam name="T">期望的 'data' 字段的具体类型。</typeparam>
    [JsonObject]
    public class BackendApiResponse<T>
    {
        /// <summary>
        /// 后端返回的状态码
        /// </summary>
        [JsonProperty("code")]
        public int Code { get; private set; }

        /// <summary>
        /// 后端返回的布尔成功标记。
        /// </summary>
        [JsonProperty("success")]
        public bool Success { get; private set; }

        /// <summary>
        /// 成功时携带的具体数据。如果操作失败或无数据返回，此字段可能为null。
        /// </summary>
        [JsonProperty("data")]
        public T Data { get; private set; }

        /// <summary>
        /// 失败时返回的用户友好错误信息。
        /// </summary>
        [JsonProperty("message", NullValueHandling = NullValueHandling.Ignore)]
        public string Message { get; private set; }

        /// <summary>
        /// 失败时返回的、用于程序化处理的错误键（例如，用于本地化）。
        /// </summary>
        [JsonProperty("msg_key", NullValueHandling = NullValueHandling.Ignore)]
        public string MsgKey { get; private set; }
    }
    #region obslete
    /// <summary>
    /// API响应的通用包装器。
    /// </summary>
    /// <typeparam name="T">data字段的具体数据类型。</typeparam>
    public class ApiResponse<T>
    {
        [JsonProperty("success")] public bool Success { get; set; }
        [JsonProperty("message")] public string Message { get; set; }
        [JsonProperty("data")] public T Data { get; set; }
        [JsonProperty("timestamp")] public long Timestamp { get; set; }
        [JsonProperty("errorCode")] public int ErrorCode { get; set; }
        public string ToJson() => JsonConvert.SerializeObject(this);

        public static ApiResponse<T> DeSerializeObject(string json) =>
            JsonConvert.DeserializeObject<ApiResponse<T>>(json);
    }

    /// <summary>
    /// 用于表示HTTP 400 Bad Request等验证错误的模型。
    /// 遵循RFC 9110 Problem Details for HTTP APIs 格式。
    /// </summary>
    public class HttpValidationError
    {
        [JsonProperty("type")] public string Type { get; set; }
        [JsonProperty("title")] public string Title { get; set; }
        [JsonProperty("status")] public int Status { get; set; }
        [JsonProperty("errors")] public Dictionary<string, List<string>> Errors { get; set; }
        [JsonProperty("traceId")] public string TraceId { get; set; }
        public string ToJson() => JsonConvert.SerializeObject(this);

        public static HttpValidationError DeSerializeObject(string json) =>
            JsonConvert.DeserializeObject<HttpValidationError>(json);
    }
    #endregion

}
