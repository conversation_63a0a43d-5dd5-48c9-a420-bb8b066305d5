using System;
using Newtonsoft.Json;

namespace UnityEngine
{
    public class VerifyVerificationCodeRequest
    {
        [JsonProperty("token")] public string VerifyToken { get; set; }
        [JsonProperty("verify_code")] public string VerifyCode { get; set; }
        public string ToJson() => JsonConvert.SerializeObject(this);
    }
    
    public class RegisterRequest
    {
        [JsonProperty("token")] public string VerifyToken { get; set; }
        [JsonProperty("password")] public string Password { get; set; }
        [JsonProperty("verify_code")] public string VerifyCode { get; set; }
        [JsonProperty("client_version")] public string ClientVersion { get; set; }
        public string ToJson() => JsonConvert.SerializeObject(this);
    }
    public class SetPasswordAfterSignRequest
    {
        [JsonProperty("token")] public string Token { get; set; }
        [JsonProperty("newPassword")] public string Password { get; set; }
        public SetPasswordAfterSignRequest(string token, string password)
        {
            Token = token;
            Password = password;
        }

        public string ToJson() => JsonConvert.SerializeObject(this);
    }
    
    public class ResetPasswordRequest
    {
        [JsonProperty("token")] public string VerifyToken { get; set; }
        [JsonProperty("new_password")] public string NewPassword { get; set; }
        [JsonProperty("verify_code")] public string VerifyCode { get; set; }

        public ResetPasswordRequest(string verifyToken, string newPassword, string verifyCode)
        {
            VerifyToken = verifyToken;
            NewPassword = newPassword;
            VerifyCode = verifyCode;
        }

        public string ToJson() => JsonConvert.SerializeObject(this);
    }

    public class LoginRequest
    {
        [JsonProperty("account")] public string Account { get; set; }
        [JsonProperty("password", NullValueHandling = NullValueHandling.Ignore)] public string Password { get; set; }
        [JsonProperty("verify_code", NullValueHandling = NullValueHandling.Ignore)] public string VerifyCode { get; set; }
        [JsonProperty("auto_login")] public bool AutoLogin { get; set; }
        [JsonProperty("client_version")] public string ClientVersion { get; set; }
        [JsonProperty("login_type")] public string LoginType { get; set; }
        public string ToJson() => JsonConvert.SerializeObject(this);
        public LoginRequest DeSerializeObject(string json) =>
            JsonConvert.DeserializeObject<LoginRequest>(json);
    }
    public class AutoLoginRequest
    {
        [JsonProperty("token")] public string AutoLoginToken { get; set; }
        [JsonProperty("client_version")] public string ClientVersion { get; set; }
        public string ToJson() => JsonConvert.SerializeObject(this);
        public LoginRequest DeSerializeObject(string json) =>
            JsonConvert.DeserializeObject<LoginRequest>(json);
    }
    public class GetVerificationCodeRequest
    {
        [JsonProperty("account")] public string Account { get; set; }
        [JsonProperty("type")] public string Type { get; set; }

        public GetVerificationCodeRequest(string account, string type)
        {
            Account = account;
            Type = type;
        }

        public string ToJson() => JsonConvert.SerializeObject(this);

        public GetVerificationCodeRequest DeSerializeObject(string json) =>
            JsonConvert.DeserializeObject<GetVerificationCodeRequest>(json);
    }

    /// <summary>
    /// 登录成功时，data字段的具体数据结构。
    /// </summary>
    public class LoginSuccessData
    {
        /// <summary>
        /// 登录token
        /// </summary>
        [JsonProperty("login_token")] 
        public string LoginToken { get; set; }
        /// <summary>
        /// 自动登录token
        /// </summary>
        [JsonProperty("persistent_token")] 
        public string PersistentToken { get; set; }
        /// <summary>
        /// 后端的用户数据
        /// </summary>
        [JsonProperty("user")] 
        public BackendApiUserData User { get; set; }
    }

    /// <summary>
    /// 验证码校验成功时，data字段的具体数据结构。
    /// </summary>
    public class VerifyVerificationCodeSuccessData
    {
        [JsonProperty("account")] public string Account { get; set; }
        [JsonProperty("type")] public string Type { get; set; }
    }
    /// <summary>
    /// 登录成功时，data字段的具体数据结构。
    /// </summary>
    public class SetPasswordSuccessData
    {
    }
    /// <summary>
    /// 登录成功时，data字段的具体数据结构。
    /// </summary>
    public class RegisterSuccessData
    {

    }
    /// <summary>
    /// 后端定义的用户数据
    /// </summary>
    public class BackendApiUserData
    {
        /// <summary>
        /// 登录token
        /// </summary>
        [JsonProperty("id")] 
        public string ID { get; set; }
        /// <summary>
        /// 自动登录token
        /// </summary>
        [JsonProperty("is_banned")] 
        public bool IsBanned { get; set; }
        /// <summary>
        /// 自动登录token
        /// </summary>
        [JsonProperty("is_temporary")] 
        public bool IsTemporary { get; set; }
        /// <summary>
        /// 自动登录token
        /// </summary>
        [JsonProperty("name")] 
        public string Name { get; set; }
        /// <summary>
        /// 自动登录token
        /// player,viewer,admin
        /// </summary>
        [JsonProperty("role")] 
        public string Role { get; set; }
        
    }
    /// <summary>
    /// 重置密码成功时，data字段的具体数据结构。
    /// </summary>
    public class ResetSuccessData
    {
    }
    /// <summary>
    /// 获取验证码成功时，data字段的具体数据结构。
    /// </summary>
    public class GetVerificationCodeSuccessData
    {
        /// <summary>
        /// 获取验证码成功返回的验证token
        /// </summary>
        [JsonProperty("token")] 
        public string VerifyToken { get; set; }
    }

    /// <summary>
    /// 用户信息模型。
    /// </summary>
    public class User
    {
        [JsonProperty("id")] public int Id { get; set; } //用户ID
        [JsonProperty("snowflakeId")] public string SnowflakeId { get; set; } //用户唯一ID
        [JsonProperty("name")] public string Name { get; set; } //用户名
        [JsonProperty("account")] public string Account { get; set; } //
        [JsonProperty("email")] public string Email { get; set; } //email
        [JsonProperty("phone")] public string Phone { get; set; } //手机
        [JsonProperty("deviceId")] public string DeviceId { get; set; } //VR设备ID
        [JsonProperty("role")] public int Role { get; set; } //玩家类型
        [JsonProperty("roleName")] public string RoleName { get; set; } // 玩家类型名
        [JsonProperty("accountType")] public int AccountType { get; set; } //账号类型
        [JsonProperty("accountTypeName")] public string AccountTypeName { get; set; } //账号类型名
        [JsonProperty("lastLogin")] public DateTime? LastLogin { get; set; } //最后登录时间
        [JsonProperty("createdAt")] public DateTime CreatedAt { get; set; } //创建时间
        [JsonProperty("updatedAt")] public DateTime UpdatedAt { get; set; } //更新时间
        [JsonProperty("isBanned")] public bool IsBanned { get; set; } //是否被封禁
        [JsonProperty("banReason")] public string BanReason { get; set; } //被封禁的原因
        [JsonProperty("unbanAt")] public DateTime? UnbanAt { get; set; } //解封时间
        [JsonProperty("isPermanentlyBanned")] public bool IsPermanentlyBanned { get; set; } //是否是被管理员封禁

        [JsonProperty("banTimeRemaining")]
        public string BanTimeRemaining { get; set; } // 使用string以处理null或未来的时间格式
    }
}
