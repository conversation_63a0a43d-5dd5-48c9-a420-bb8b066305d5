using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using UnityEngine.Networking;
using Newtonsoft.Json.Linq;
using System.Linq;

/*example
    public class PlayerProfile
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public class GameClient : MonoBehaviour
    {
        private async void Start()
        {
            var response = await HttpHelper.Create().WithTimeout(30) // 设置30秒超时
                .WithBearerToken("your_jwt_token_here").GetAsync<PlayerProfile>("http://your-api.com/player/123");
            response.OnSuccess(player => { Debug.Log($"成功获取玩家信息: {player.Name} (ID: {player.Id})"); })
                .OnFail((error, code) => { Debug.LogError($"请求失败 (状态码: {code}): {error}"); });
        }
    }
    public class LoginRequest
    {
        public string Username { get; set; }
        public string Password { get; set; }
    }

    public class LoginResponse
    {
        public string Token { get; set; }
    }

    public class AuthService : MonoBehaviour
    {
        public async UniTask<string> LoginAsync(string username, string password)
        {
            var loginData = new LoginRequest { Username = username, Password = password };

            var response = await HttpHelper.Create()
                .WithBody(loginData) // 自动序列化为JSON并设置Content-Type
                .PostAsync<LoginResponse>("http://your-api.com/auth/login");

            if (response.IsSuccess)
            {
                Debug.Log("登录成功！");
                return response.Data.Token;
            }
            else
            {
                Debug.LogError($"登录失败: {response.ErrorMessage}");
                return null;
            }
        }
    }
*/

namespace UnityEngine
{
    /// <summary>
    /// 一个基于 UnityWebRequest 和 UniTask 的现代化 HTTP 工具类。
    /// 支持链式调用，简化 RESTful API 请求。
    /// </summary>
    public class HttpHelper
    {
        /// <summary>
        /// 定义一个请求预处理器委托。它可以在请求发送前异步修改URL和请求体。
        /// </summary>
        /// <returns>返回一个包含新URL和新请求体的元组。</returns>
        public delegate UniTask<(string newUrl, string newBody)> RequestPreprocessor(string originalUrl, string originalBody);

        private readonly Dictionary<string, string> _headers = new Dictionary<string, string>();
        private string _body;
        private int _timeout = 5; // 默认超时时间为5秒
        private CancellationTokenSource _cts;
        private RequestPreprocessor _preprocessor;

        /// <summary>
        /// 静态入口，创建一个新的 HttpHelper 实例以便于链式调用。
        /// </summary>
        public static HttpHelper Create() => new HttpHelper();

        #region --- 链式配置方法 ---

        /// <summary>
        /// 添加或更新一个 HTTP 请求头。
        /// </summary>
        public HttpHelper WithHeader(string key, string value)
        {
            _headers[key] = value;
            return this;
        }
        
        /// <summary>
        /// 将T序列化为json后使用json作为body
        /// </summary>
        public HttpHelper WithBody<T>(T dataObject)
        {
            _body = JsonConvert.SerializeObject(dataObject);
            return WithHeader("Content-Type", "application/json");
        }

        /// <summary>
        /// 将一个对象序列化为 JSON 字符串，并设置其为请求体。
        /// 同时自动添加 "Content-Type: application/json" 请求头。
        /// </summary>
        public HttpHelper WithJsonBody(string jsonBody)
        {
            if (string.IsNullOrEmpty(jsonBody))
            {
                Debug.LogError(
                    $"[{GetType().FullName}::WithJsonBody] {jsonBody}: JSON body string cannot be null or empty.");
            }

            try
            {
                var token = JToken.Parse(jsonBody);
                if (token.Type != JTokenType.Object)
                {
                    Debug.LogError(
                        $"[{GetType().FullName}::WithJsonBody] {jsonBody}: The provided string is valid JSON, but it is not a JSON object.");
                }
            }
            catch (JsonException ex)
            {
                Debug.LogError(
                    $"[{GetType().FullName}::WithJsonBody] {jsonBody}: The provided string is not a valid JSON format.");
            }

            _body = jsonBody;
            WithHeader("Content-Type", "application/json");
            return this;
        }

        /// <summary>
        /// 设置 User-Agent 请求头。
        /// </summary>
        public HttpHelper WithUserAgent(string userAgent)
        {
            return WithHeader("User-Agent", userAgent);
        }

        /// <summary>
        /// 添加 Bearer Token 认证头。
        /// </summary>
        public HttpHelper WithBearerToken(string token)
        {
            return WithHeader("Authorization", $"Bearer {token}");
        }

        /// <summary>
        /// 设置请求的超时时间（单位：秒）。
        /// </summary>
        public HttpHelper WithTimeout(int seconds)
        {
            _timeout = seconds;
            return this;
        }

        /// <summary>
        /// 关联一个 CancellationTokenSource 以便能够取消请求。
        /// </summary>
        public HttpHelper WithCancellation(CancellationTokenSource cts)
        {
            _cts = cts;
            return this;
        }
        
        /// <summary>
        /// 为此请求配置一个预处理器，它将在请求发送前执行。
        /// </summary>
        public HttpHelper WithPreprocessor(RequestPreprocessor preprocessor)
        {
            _preprocessor = preprocessor;
            return this;
        }

        #endregion

        #region --- REST API 执行方法 ---

        public async UniTask<HttpResponse<T>> GetAsync<T>(string url)
        {
            return await SendRequestAsync<T>(url, UnityWebRequest.kHttpVerbGET);
        }

        public async UniTask<HttpResponse<T>> PostAsync<T>(string url)
        {
            return await SendRequestAsync<T>(url, UnityWebRequest.kHttpVerbPOST);
        }

        public async UniTask<HttpResponse<T>> PutAsync<T>(string url)
        {
            return await SendRequestAsync<T>(url, UnityWebRequest.kHttpVerbPUT);
        }

        public async UniTask<HttpResponse<T>> DeleteAsync<T>(string url)
        {
            return await SendRequestAsync<T>(url, UnityWebRequest.kHttpVerbDELETE);
        }

        #endregion

        private async UniTask<HttpResponse<T>> SendRequestAsync<T>(string url, string method)
        {
            if (_preprocessor != null)
            {
                // 将原始URL和请求体传给预处理器，并用其返回的结果覆盖当前值
                var (processedUrl, processedBody) = await _preprocessor(url, _body);
                url = processedUrl;
                _body = processedBody;
            }
            using (var request = new UnityWebRequest(url, method))
            {
                // 配置请求体 (如果存在)
                if (!string.IsNullOrEmpty(_body) && (method == UnityWebRequest.kHttpVerbPOST ||
                                                     method == UnityWebRequest.kHttpVerbPUT))
                {
                    var bodyRaw = Encoding.UTF8.GetBytes(_body);
                    request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                }

                // 所有请求都需要 DownloadHandler 来接收响应
                request.downloadHandler = new DownloadHandlerBuffer();

                // 添加所有配置的请求头
                foreach (var header in _headers)
                {
                    request.SetRequestHeader(header.Key, header.Value);
                }

                // 设置超时
                request.timeout = _timeout;
                try
                {
                    // 发送请求并异步等待，同时允许外部取消
                    await request.SendWebRequest().WithCancellation(_cts?.Token ?? default);
                    // 无论成功 (2xx) 还是失败 (4xx/5xx)，只要有响应体，我们都尝试解析
                    // 这是最关键的逻辑简化
                    string rawResponseText = request.downloadHandler.text;
                    T responseData = default;
                    if (!string.IsNullOrEmpty(rawResponseText))
                    {
                        try
                        {
                            responseData = JsonConvert.DeserializeObject<T>(rawResponseText);
                        }
                        catch (Exception ex)
                        {
                            // 如果JSON解析失败，记录一个警告，responseData 保持为 default
                            Debug.LogWarning(
                                $"[HttpHelper] JSON deserialization failed for a {request.responseCode} response. Error: {ex.Message}. Raw text: {rawResponseText}");
                        }
                    }

                    // 使用 UnityWebRequest.result 来判断最终的 IsSuccess 状态
                    // 这能正确处理2xx和非2xx的情况
                    bool isSuccess = request.result == UnityWebRequest.Result.Success;

                    // 统一返回，将所有信息都封装好
                    return new HttpResponse<T>(isSuccess, responseData, rawResponseText, request.responseCode,
                        rawResponseText);
                }
                catch (UnityWebRequestException e)
                {
                    // 这是由非2xx状态码（如400, 500）或网络问题（如DNS无法解析）触发的异常
                    // 我们现在尝试像处理成功响应一样，去解析错误响应体。
                    string rawResponseText = e.UnityWebRequest.downloadHandler?.text;
                    if (!string.IsNullOrEmpty(rawResponseText))
                    {
                        Debug.LogError("Http请求失败！" + rawResponseText);
                        return new HttpResponse<T>(false, default, rawResponseText, e.ResponseCode,
                            rawResponseText);
                    }
                    else
                    {
                        // 如果连错误响应体都没有，直接返回失败。
                        return new HttpResponse<T>(false, default, e.Message, e.ResponseCode, null);
                    }
                }
                catch (OperationCanceledException)
                {
                    // 客户端取消操作
                    return new HttpResponse<T>(false, default, "Request was canceled by the client.", -1,
                        null);
                }
                catch (Exception ex)
                {
                    // 其他所有未预料到的客户端异常
                    return new HttpResponse<T>(false, default,
                        $"An unexpected client-side error occurred: {ex.Message}", -1, null);
                }
            }
        }
        
        /// <summary>
        /// 向URL添加或更新一个查询参数，不依赖任何外部库。
        /// </summary>
        /// <param name="url">原始URL。</param>
        /// <param name="key">要添加或更新的参数键。</param>
        /// <param name="value">要设置的参数值。</param>
        /// <returns>带有新参数的新URL。</returns>
        public static string AddOrUpdateQueryParameter(string url, string key, string value)
        {
            // 将URL分割为基础部分和查询部分
            string baseUrl = url;
            string queryString = string.Empty;
            int queryIndex = url.IndexOf('?');
            if (queryIndex != -1)
            {
                baseUrl = url.Substring(0, queryIndex);
                queryString = url.Substring(queryIndex + 1);
            }

            // 解析现有查询参数到字典中
            var queryParams = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(queryString))
            {
                string[] pairs = queryString.Split('&');
                foreach (string pair in pairs)
                {
                    int equalsIndex = pair.IndexOf('=');
                    if (equalsIndex != -1)
                    {
                        string paramKey = pair.Substring(0, equalsIndex);
                        string paramValue = pair.Substring(equalsIndex + 1);
                        queryParams[paramKey] = paramValue;
                    }
                    else
                    {
                        queryParams[pair] = string.Empty;
                    }
                }
            }

            queryParams[key] = value;
            var newQueryString = string.Join("&",
                queryParams.Select(
                    kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"));
            return $"{baseUrl}?{newQueryString}";
        }
    }

    /// <summary>
    /// 封装 HTTP 响应结果，支持链式回调。
    /// </summary>
    /// <typeparam name="T">期望的响应数据类型</typeparam>
    public class HttpResponse<T>
    {
        public bool IsSuccess { get; }
        public T Data { get; }
        public string ErrorMessage { get; }
        public long StatusCode { get; }
        public string RawResponse { get; }

        public HttpResponse(bool isSuccess, T data, string errorMessage, long statusCode, string rawResponse)
        {
            IsSuccess = isSuccess;
            Data = data;
            ErrorMessage = errorMessage;
            StatusCode = statusCode;
            RawResponse = rawResponse;
        }

        /// <summary>
        /// 如果请求成功，则执行此回调。
        /// </summary>
        public HttpResponse<T> OnSuccess(Action<T> action)
        {
            if (IsSuccess)
            {
                action?.Invoke(Data);
            }

            return this;
        }

        /// <summary>
        /// 如果请求失败，则执行此回调。
        /// </summary>
        public HttpResponse<T> OnFail(Action<string, long> action)
        {
            if (!IsSuccess)
            {
                action?.Invoke(ErrorMessage, StatusCode);
            }

            return this;
        }
    }
}
