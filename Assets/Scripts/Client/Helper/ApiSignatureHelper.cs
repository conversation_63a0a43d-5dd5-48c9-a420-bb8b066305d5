using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace UnityEngine
{
    /// <summary>
    /// 负责API签名逻辑的独立服务类。
    /// </summary>
    public class ApiSignatureHelper
    {
        private readonly string _secretKey;

        /// <summary>
        /// 构造一个API签名器实例。
        /// </summary>
        /// <param name="secretKey">用于签名的密钥。</param>
        public ApiSignatureHelper(string secretKey)
        {
            if (string.IsNullOrEmpty(secretKey))
            {
                Debug.LogError("私钥为空！");
            }

            _secretKey = secretKey;
        }

        /// <summary>
        /// 获取一个符合 HttpHelper 规范的请求预处理器。
        /// </summary>
        public HttpHelper.RequestPreprocessor GetPreprocessor()
        {
            return (originalUrl, originalBody) =>
            {
                var (newUrl, newBody) = Process(originalUrl, originalBody);
                return UniTask.FromResult((newUrl, newBody));
            };
        }

        /// <summary>
        /// 执行签名处理的核心逻辑
        /// 签名方法后端指定，此处仅翻译为unity使用的版本
        /// </summary>
        private (string newUrl, string newBody) Process(string url, string body)
        {
            // 如果没有请求体，则无需签名，直接返回原始数据
            if (string.IsNullOrEmpty(body))
            {
                return (url, body);
            }

            try
            {
                var jsonData = JObject.Parse(body);

                // 检查签名条件：存在 client_version 字段且值不为 "web"
                if (jsonData.TryGetValue("client_version", out var clientVersionToken) &&
                    clientVersionToken.ToString() != "web")
                {
                    long timestamp = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds();
                    string stringToSign = _secretKey + timestamp + body;
                    string sign = CalculateMD5(stringToSign).ToUpper();
                    Debug.Log($"[ApiSignature] String to Sign: {stringToSign}");
                    string signatureParamValue = $"{timestamp}-{sign}";
                    string newUrl = HttpHelper.AddOrUpdateQueryParameter(url, "sign", signatureParamValue);
                    return (newUrl, body);
                }
            }
            catch (Exception ex)
            {
                // 如果JSON解析失败或发生其他错误，则不签名，并记录警告
                Debug.LogWarning($"[ApiSignature] Skipping signature due to an error: {ex.Message}");
            }

            // 如果不满足签名条件，返回原始URL和请求体
            return (url, body);
        }

        private static string CalculateMD5(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);
                var sb = new StringBuilder();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    sb.Append(hashBytes[i].ToString("x2"));
                }

                return sb.ToString();
            }
        }
    }
}
