namespace UnityEngine
{
    public class ApiHelper
    {
        private readonly static string BaseApi = "http://tfg-auth-test.vvslink.cn";
        public readonly static string QA_SECRET_KEY = "CFC1FFE842C9D4660D20B1AD6E19";
        #region QA_Api
        /// <summary>
        /// 发送验证码接口。
        /// </summary>
        private readonly static string QA_SendVerificationCode = "/api/auth/verification";
        /// <summary>
        /// 校验验证码接口
        /// </summary>
        private readonly static string QA_CheckVerificationCode = "/api/auth/verification/check";
        /// <summary>
        /// 用户注册接口。
        /// </summary>
        private readonly static string QA_Register = "/api/auth/register";
        /// <summary>
        /// 密码重置接口。
        /// </summary>
        private readonly static string QA_ResetPassword = "/api/auth/reset-password";
        /// <summary>
        /// 密码登录接口。
        /// </summary>
        private readonly static string QA_Login = "/api/auth/login";
        /// <summary>
        /// 自动登录接口。
        /// </summary>
        private readonly static string QA_AutoLogin = "/api/auth/login/persistent";

        #endregion
        public static string QA_GetSendVerificationCodeApi => string.Concat(BaseApi, QA_SendVerificationCode);
        public static string QA_GetRegisterApi => string.Concat(BaseApi, QA_Register);
        public static string QA_GetCheckVerificationCodeApi => string.Concat(BaseApi, QA_CheckVerificationCode);
        public static string QA_GetLoginApi => string.Concat(BaseApi, QA_Login);
        public static string QA_GetResetPasswordApi => string.Concat(BaseApi, QA_ResetPassword);
        public static string QA_GetAutoLoginApi => string.Concat(BaseApi, QA_AutoLogin);
    }
}
