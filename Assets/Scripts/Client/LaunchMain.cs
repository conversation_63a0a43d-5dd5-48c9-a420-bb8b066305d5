using Client.System.Battle;
using Cysharp.Threading.Tasks;
using GameMain;
using QFramework;
using QFramework.PrefsKit;
using TFG.UI;
using UnityEngine;
using UnityEngine.SceneManagement;
using GameClient.System;

namespace GameClient
{
    public partial class LaunchMain : IController
    {
        private static readonly LaunchMain _instance = new();
        public static void Main()
        {
            LogKit.I("[GameClient::LaunchMain::Main] Begin");
            Application.targetFrameRate = 10240;

            _instance.InitAsync().Forget();

            LogKit.I($"[GameClient::LaunchMain::Main] End");
        }

        private async UniTask InitAsync()
        {
            PrefsKitUtil.PrefsUtil.SetSalt("TFGGame");
            
            await LaunchMainArch.Interface.InitAsync();

            UIKit.ClosePanel<UIDownloadPanel>();  
            
            var resLoader = ResLoader.Allocate();
            await resLoader.LoadSceneUniTask("ClientInit", LoadSceneMode.Single);
            LogKit.I($"[{GetType().FullName}::InitAsync] Scene Init loaded");

            if (GameRoot.Instance.GetSystem<PlatformSystem>().IsPlatformXr())
            {
                await UIKitExtensions.OpenPosturalGuidancePanel( async () =>
                {
                    LogKit.I("[UIInitPanel] Postural guidance finished!");
                    await UIKit.OpenPanel<UIInitPanel>();
                });
            }
            else
            {
                await UIKit.OpenPanel<UIInitPanel>();
            }
        }

        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
    }
}
