using System;
using System.Collections.Generic;
using System.Linq;
using GameShare.Utility;
using Newtonsoft.Json;
using QFramework;
using TFGShare.Utility;

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势模板，包含完整的手势数据和配置信息
    /// </summary>
    [Serializable]
    public class GestureTemplate
    {
        /// <summary>
        /// 手势名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 时间戳运动数据序列
        /// </summary>
        public List<TimestampedMovementData> Sequence { get; set; }
        
        /// <summary>
        /// 手势元数据（包含关节轨迹信息）
        /// </summary>
        public GestureMetadata Metadata { get; set; }
        
        /// <summary>
        /// 匹配阈值
        /// </summary>
        public float MatchThreshold { get; set; } = 0.8f;
        
        /// <summary>
        /// 获取手势持续时间（毫秒）
        /// </summary>
        /// <returns>持续时间</returns>
        public float GetDurationMs()
        {
            if (Sequence == null || Sequence.Count == 0)
            {
                return 0f;
            }
            
            return Sequence.Last().TimestampMs - Sequence.First().TimestampMs;
        }
        
        /// <summary>
        /// 转换为JSON字符串（参考 PoseSystemStorage.SaveTemplate）
        /// </summary>
        /// <returns>JSON字符串</returns>
        public string ToJson()
        {
            try
            {
                var settings = JsonConverterHelper.GetDefaultSettings();
                return JsonConvert.SerializeObject(this, Formatting.Indented, settings);
            }
            catch (Exception ex)
            {
                LogKit.E($"[GestureTemplate::ToJson] Error serializing template: {ex.Message}");
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 从JSON字符串创建手势模板
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>手势模板对象</returns>
        public static GestureTemplate FromJson(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                {
                    return null;
                }
                
                var settings = JsonConverterHelper.GetDefaultSettings();
                return JsonConvert.DeserializeObject<GestureTemplate>(json, settings);
            }
            catch (Exception ex)
            {
                LogKit.E($"[GestureTemplate::FromJson] Error deserializing template: {ex.Message}");
                return null;
            }
        }
    }
}
