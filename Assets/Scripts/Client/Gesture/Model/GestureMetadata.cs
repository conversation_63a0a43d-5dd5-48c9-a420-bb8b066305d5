using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势元数据，重点支持关节点运动轨迹分析
    /// </summary>
    [Serializable]
    public class GestureMetadata
    {
        /// <summary>
        /// 手势持续时间（毫秒）
        /// </summary>
        public float Duration { get; set; }

        /// <summary>
        /// 关节点位置轨迹数据
        /// </summary>
        public Dictionary<string, List<Vector3>> JointTrajectories { get; set; } = new();

        /// <summary>
        /// 关节点旋转轨迹数据
        /// </summary>
        public Dictionary<string, List<Quaternion>> JointRotationTrajectories { get; set; } = new();

        /// <summary>
        /// 关注的关节点列表
        /// </summary>
        public List<string> FocusedJoints { get; set; } = new();

        /// <summary>
        /// 是否使用头部朝向作为参考系（原始需求：以头部朝向为方向计算）
        /// </summary>
        public bool UseHeadOrientation { get; set; } = true;

        /// <summary>
        /// 从序列中计算关节轨迹
        /// </summary>
        /// <param name="sequence">时间戳运动数据序列</param>
        public void CalculateFromSequence(List<TimestampedMovementData> sequence)
        {
            if (sequence == null || sequence.Count == 0)
            {
                return;
            }

            // 计算持续时间
            Duration = sequence.Last().TimestampMs - sequence.First().TimestampMs;

            // 清空现有轨迹
            JointTrajectories.Clear();
            JointRotationTrajectories.Clear();
            FocusedJoints.Clear();

            // 提取各关节点的轨迹
            foreach (var data in sequence)
            {
                if (data.MovementData?.Datas != null)
                {
                    for (var i = 0; i < data.MovementData.Datas.Length; i++)
                    {
                        var jointName = GetJointName(i);

                        // 位置轨迹
                        if (!JointTrajectories.ContainsKey(jointName))
                        {
                            JointTrajectories[jointName] = new List<Vector3>();
                        }

                        var position = new Vector3(data.MovementData.Datas[i].Pos.x, data.MovementData.Datas[i].Pos.y,
                                                   data.MovementData.Datas[i].Pos.z);
                        JointTrajectories[jointName].Add(position);

                        // 旋转轨迹
                        if (!JointRotationTrajectories.ContainsKey(jointName))
                        {
                            JointRotationTrajectories[jointName] = new List<Quaternion>();
                        }

                        var rotation = new Quaternion(
                                                      data.MovementData.Datas[i].Rot.x,
                                                      data.MovementData.Datas[i].Rot.y,
                                                      data.MovementData.Datas[i].Rot.z,
                                                      data.MovementData.Datas[i].Rot.w);
                        JointRotationTrajectories[jointName].Add(rotation);

                        // 添加到关注关节列表
                        if (!FocusedJoints.Contains(jointName))
                        {
                            FocusedJoints.Add(jointName);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取特定关节的位置轨迹
        /// </summary>
        /// <param name="jointName">关节名称</param>
        /// <returns>位置轨迹列表</returns>
        public List<Vector3> GetJointTrajectory(string jointName)
        {
            return JointTrajectories.GetValueOrDefault(jointName, new List<Vector3>());
        }

        /// <summary>
        /// 获取特定关节的旋转轨迹
        /// </summary>
        /// <param name="jointName">关节名称</param>
        /// <returns>旋转轨迹列表</returns>
        public List<Quaternion> GetJointRotationTrajectory(string jointName)
        {
            return JointRotationTrajectories.GetValueOrDefault(jointName, new List<Quaternion>());
        }

        /// <summary>
        /// 根据索引获取关节名称
        /// </summary>
        /// <param name="index">关节索引</param>
        /// <returns>关节名称</returns>
        private string GetJointName(int index)
        {
            // 根据实际的关节映射来实现
            var jointNames = new[]
                             {
                                 "Head", "Neck", "LeftShoulder", "RightShoulder",
                                 "LeftElbow", "RightElbow", "LeftHand", "RightHand",
                                 "Spine", "Hips", "LeftKnee", "RightKnee",
                                 "LeftFoot", "RightFoot"
                             };

            return index < jointNames.Length ? jointNames[index] : $"Joint_{index}";
        }
    }
}
