using System;
using System.Collections.Generic;
using GameShare.Events;
using QFramework;
using TFGShare.Protocol;
using UnityEngine;

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势录制系统，负责手势数据的录制、存储和管理
    /// 根据用户反馈修正：事件注册在StartRecording中进行，手势名称使用时间戳格式
    /// </summary>
    public class GestureRecordSystem : AbstractSystem
    {
        #region 私有字段
        
        /// <summary>
        /// 手势系统配置
        /// </summary>
        private GestureSystemConfig _config;
        
        /// <summary>
        /// 当前是否正在录制
        /// </summary>
        private bool _isRecording = false;
        
        /// <summary>
        /// 录制的移动数据列表
        /// </summary>
        private readonly List<MovementData> _recordedMovements = new();
        
        /// <summary>
        /// 录制的时间戳移动数据序列
        /// </summary>
        private readonly List<TimestampedMovementData> _recordedSequence = new();
        
        /// <summary>
        /// 当前手势名称
        /// </summary>
        private string _currentGestureName = "NewGesture";
        
        #endregion
        
        #region 系统生命周期
        
        /// <summary>
        /// 系统初始化
        /// 注意：根据用户反馈，不在这里注册BattlePlayerMovementEvent
        /// </summary>
        protected override void OnInit()
        {
            try
            {
                // 加载手势系统配置
                _config = Resources.Load<GestureSystemConfig>("GestureSystemConfig");
                if (_config == null)
                {
                    LogKit.E($"[{GetType().FullName}::OnInit] Failed to load GestureSystemConfig");
                    return;
                }
                
                // 重要：不在这里注册BattlePlayerMovementEvent
                // 事件注册将在StartRecording()中进行，以实现精确的事件控制
                
                LogKit.I($"[{GetType().FullName}::OnInit] GestureRecordSystem initialized successfully");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnInit] Error during initialization: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 系统清理
        /// </summary>
        protected override void OnDeinit()
        {
            try
            {
                // 如果正在录制，停止录制并清理
                if (_isRecording)
                {
                    LogKit.W($"[{GetType().FullName}::OnDeinit] Recording was active during deinit, stopping recording");
                    // 注意：这里不直接调用StopRecording()，因为它可能依赖其他已清理的资源
                    _isRecording = false;
                    // 手动注销事件（如果已注册）
                    try
                    {
                        this.UnRegisterEvent<BattlePlayerMovementEvent>(OnBattlePlayerMovement);
                    }
                    catch
                    {
                        // 忽略注销失败，可能事件未注册
                    }
                }
                
                // 清理录制数据
                _recordedMovements.Clear();
                _recordedSequence.Clear();
                
                LogKit.I($"[{GetType().FullName}::OnDeinit] GestureRecordSystem deinitialized successfully");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnDeinit] Error during cleanup: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 私有方法占位符
        
        /// <summary>
        /// 处理玩家移动事件（将在后续任务中实现）
        /// </summary>
        private void OnBattlePlayerMovement(BattlePlayerMovementEvent e)
        {
            // 实现将在下一个任务中添加
        }
        
        #endregion
    }
}
