using System;

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势回放开始事件
    /// </summary>
    public class GesturePlaybackStartedEvent
    {
        /// <summary>
        /// 手势名称
        /// </summary>
        public string GestureName { get; set; }
        
        /// <summary>
        /// 总持续时间（秒）
        /// </summary>
        public float Duration { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }
    }

    /// <summary>
    /// 手势回放停止事件
    /// </summary>
    public class GesturePlaybackStoppedEvent
    {
        /// <summary>
        /// 手势名称
        /// </summary>
        public string GestureName { get; set; }
        
        /// <summary>
        /// 停止时间
        /// </summary>
        public DateTime StopTime { get; set; }
        
        /// <summary>
        /// 已播放时长（秒）
        /// </summary>
        public float PlayedDuration { get; set; }
    }

    /// <summary>
    /// 手势回放进度事件
    /// </summary>
    public class GesturePlaybackProgressEvent
    {
        /// <summary>
        /// 当前播放时间（秒）
        /// </summary>
        public float CurrentTime { get; set; }
        
        /// <summary>
        /// 总时间（秒）
        /// </summary>
        public float TotalTime { get; set; }
        
        /// <summary>
        /// 当前播放的数据
        /// </summary>
        public TimestampedMovementData CurrentData { get; set; }
        
        /// <summary>
        /// 播放进度（0-1）
        /// </summary>
        public float Progress { get; set; }
    }


}
