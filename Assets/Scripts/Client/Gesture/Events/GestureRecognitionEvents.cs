using System;
using System.Collections.Generic;

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势识别成功事件
    /// </summary>
    public class GestureRecognizedEvent
    {
        /// <summary>
        /// 识别的手势名称
        /// </summary>
        public string GestureName { get; set; }
        
        /// <summary>
        /// 相似度分数（0-1）
        /// </summary>
        public float Similarity { get; set; }
        
        /// <summary>
        /// 匹配的模板
        /// </summary>
        public GestureTemplate MatchedTemplate { get; set; }
        
        /// <summary>
        /// 识别时间
        /// </summary>
        public DateTime RecognitionTime { get; set; }
    }

    /// <summary>
    /// 手势识别实时更新事件（用于相似度实时显示）
    /// </summary>
    public class GestureRecognitionUpdateEvent
    {
        /// <summary>
        /// 手势名称
        /// </summary>
        public string GestureName { get; set; }
        
        /// <summary>
        /// 当前相似度分数（0-1）
        /// </summary>
        public float Similarity { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }
    }

    /// <summary>
    /// 手势识别开始事件
    /// </summary>
    public class GestureRecognitionStartedEvent
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 加载的模板列表
        /// </summary>
        public List<string> LoadedTemplates { get; set; }
    }

    /// <summary>
    /// 手势识别停止事件
    /// </summary>
    public class GestureRecognitionStoppedEvent
    {
        /// <summary>
        /// 停止时间
        /// </summary>
        public DateTime StopTime { get; set; }
        
        /// <summary>
        /// 总识别次数
        /// </summary>
        public int TotalRecognitions { get; set; }
    }
}
