using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using GameClient.Gesture;
using QFramework;
using TFGShare.Utility;
using UnityEngine;

namespace TFGClient.Gesture.Threading
{
    /// <summary>
    /// 多线程识别处理器
    /// 使用多线程进行识别计算，主线程负责消息发送
    /// </summary>
    public class ThreadedRecognitionProcessor : IDisposable
    {
        // 线程安全的队列
        private readonly ConcurrentQueue<RecognitionTask> _taskQueue = new ConcurrentQueue<RecognitionTask>();
        private readonly ConcurrentQueue<RecognitionResult> _resultQueue = new ConcurrentQueue<RecognitionResult>();
        
        // 线程控制
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        private readonly List<Task> _workerTasks = new List<Task>();
        private readonly object _lockObject = new object();
        
        // 配置
        private readonly int _workerThreadCount;
        private readonly GestureSystemConfig _config;
        
        // 状态
        private bool _isRunning = false;
        private bool _disposed = false;
        
        // 性能统计
        private long _totalTasksProcessed = 0;
        private long _totalProcessingTime = 0;
        
        public ThreadedRecognitionProcessor(GestureSystemConfig config, int workerThreadCount = 2)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _workerThreadCount = Math.Max(1, Math.Min(workerThreadCount, Environment.ProcessorCount));
            
            LogKit.I($"[{GetType().FullName}] Initialized with {_workerThreadCount} worker threads");
        }
        
        /// <summary>
        /// 启动多线程处理器
        /// </summary>
        public void Start()
        {
            if (_isRunning || _disposed) return;
            
            lock (_lockObject)
            {
                if (_isRunning) return;
                
                _isRunning = true;
                
                // 启动工作线程
                for (int i = 0; i < _workerThreadCount; i++)
                {
                    var workerId = i;
                    var task = Task.Run(() => WorkerThreadLoop(workerId), _cancellationTokenSource.Token);
                    _workerTasks.Add(task);
                }
                
                LogKit.I($"[{GetType().FullName}::Start] Started {_workerThreadCount} worker threads");
            }
        }
        
        /// <summary>
        /// 停止多线程处理器
        /// </summary>
        public void Stop()
        {
            if (!_isRunning) return;
            
            lock (_lockObject)
            {
                if (!_isRunning) return;
                
                _isRunning = false;
                _cancellationTokenSource.Cancel();
                
                try
                {
                    Task.WaitAll(_workerTasks.ToArray(), TimeSpan.FromSeconds(5));
                }
                catch (AggregateException ex)
                {
                    LogKit.W($"[{GetType().FullName}::Stop] Some worker threads did not stop gracefully: {ex.Message}");
                }
                
                _workerTasks.Clear();
                LogKit.I($"[{GetType().FullName}::Stop] All worker threads stopped");
            }
        }
        
        /// <summary>
        /// 提交识别任务
        /// </summary>
        /// <param name="task">识别任务</param>
        public void SubmitTask(RecognitionTask task)
        {
            if (!_isRunning || _disposed)
            {
                LogKit.W($"[{GetType().FullName}::SubmitTask] Processor not running, task ignored");
                return;
            }
            
            _taskQueue.Enqueue(task);
            
            if (_config.enablePerformanceMonitoring)
            {
                LogKit.I($"[{GetType().FullName}::SubmitTask] Task submitted: {task.TaskId}, Queue size: {_taskQueue.Count}");
            }
        }
        
        /// <summary>
        /// 获取识别结果
        /// 在主线程中调用
        /// </summary>
        /// <returns>识别结果列表</returns>
        public List<RecognitionResult> GetResults()
        {
            var results = new List<RecognitionResult>();
            
            while (_resultQueue.TryDequeue(out var result))
            {
                results.Add(result);
            }
            
            return results;
        }
        
        /// <summary>
        /// 工作线程循环
        /// </summary>
        /// <param name="workerId">工作线程ID</param>
        private void WorkerThreadLoop(int workerId)
        {
            LogKit.I($"[{GetType().FullName}::WorkerThreadLoop] Worker {workerId} started");
            
            try
            {
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    if (_taskQueue.TryDequeue(out var task))
                    {
                        ProcessTask(task, workerId);
                    }
                    else
                    {
                        // 没有任务时短暂休眠
                        Thread.Sleep(1);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常的取消操作
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::WorkerThreadLoop] Worker {workerId} error: {ex.Message}");
            }
            
            LogKit.I($"[{GetType().FullName}::WorkerThreadLoop] Worker {workerId} stopped");
        }
        
        /// <summary>
        /// 处理识别任务
        /// </summary>
        /// <param name="task">任务</param>
        /// <param name="workerId">工作线程ID</param>
        private void ProcessTask(RecognitionTask task, int workerId)
        {
            var startTime = DateTime.UtcNow;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var similarity = DatFileManager.MatchWithDatData(
                    task.DatData, 
                    task.InputSequence, 
                    _config.enableSimilarityLogging,
                    _config.recognitionThreshold
                );
                
                stopwatch.Stop();
                
                var result = new RecognitionResult
                {
                    TaskId = task.TaskId,
                    GestureName = task.GestureName,
                    Similarity = similarity,
                    ProcessingTime = stopwatch.ElapsedMilliseconds,
                    WorkerId = workerId,
                    Timestamp = startTime,
                    Success = similarity >= _config.recognitionThreshold
                };
                
                _resultQueue.Enqueue(result);
                
                // 更新统计
                Interlocked.Increment(ref _totalTasksProcessed);
                Interlocked.Add(ref _totalProcessingTime, stopwatch.ElapsedMilliseconds);
                
                if (_config.enablePerformanceMonitoring)
                {
                    LogKit.I($"[{GetType().FullName}::ProcessTask] Worker {workerId} processed {task.GestureName}: {similarity:F3} in {stopwatch.ElapsedMilliseconds}ms");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                var errorResult = new RecognitionResult
                {
                    TaskId = task.TaskId,
                    GestureName = task.GestureName,
                    Similarity = 0f,
                    ProcessingTime = stopwatch.ElapsedMilliseconds,
                    WorkerId = workerId,
                    Timestamp = startTime,
                    Success = false,
                    ErrorMessage = ex.Message
                };
                
                _resultQueue.Enqueue(errorResult);
                LogKit.E($"[{GetType().FullName}::ProcessTask] Worker {workerId} error processing {task.GestureName}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取性能统计
        /// </summary>
        /// <returns>性能统计信息</returns>
        public PerformanceStats GetPerformanceStats()
        {
            var totalTasks = Interlocked.Read(ref _totalTasksProcessed);
            var totalTime = Interlocked.Read(ref _totalProcessingTime);
            
            return new PerformanceStats
            {
                TotalTasksProcessed = totalTasks,
                TotalProcessingTime = totalTime,
                AverageProcessingTime = totalTasks > 0 ? (float)totalTime / totalTasks : 0f,
                QueueSize = _taskQueue.Count,
                ResultQueueSize = _resultQueue.Count,
                WorkerThreadCount = _workerThreadCount,
                IsRunning = _isRunning
            };
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;
            
            Stop();
            _cancellationTokenSource?.Dispose();
            _disposed = true;
            
            LogKit.I($"[{GetType().FullName}::Dispose] ThreadedRecognitionProcessor disposed");
        }
    }
}
