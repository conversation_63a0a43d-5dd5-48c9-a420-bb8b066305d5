using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using TFGShare.Utility;

namespace TFGClient.Gesture.Tests
{
    /// <summary>
    /// 测试数据生成器
    /// 生成用于连续识别测试的dat文件和MovementData序列
    /// </summary>
    public class TestDataGenerator : MonoBehaviour
    {
        [Header("Generation Settings")]
        [Tooltip("测试数据输出目录")]
        public string outputDirectory = "Assets/GestureData/TestTemplates";
        
        [Tooltip("生成的模版数量")]
        public int templateCount = 3;
        
        [Tooltip("每个模版的数据点数量")]
        public int dataPointsPerTemplate = 7;
        
        [Tooltip("时间间隔（毫秒）")]
        public float timeIntervalMs = 100f;
        
        [Header("Movement Parameters")]
        [Tooltip("位置变化范围")]
        public Vector3 positionRange = new Vector3(2f, 2f, 2f);
        
        [Tooltip("旋转变化范围（度）")]
        public Vector3 rotationRange = new Vector3(45f, 45f, 45f);
        
        private void Start()
        {
            // 确保输出目录存在
            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
                LogKit.I($"[{GetType().FullName}::Start] Created output directory: {outputDirectory}");
            }
        }
        
        /// <summary>
        /// 生成测试模版数据
        /// </summary>
        [ContextMenu("Generate Test Templates")]
        public void GenerateTestTemplates()
        {
            try
            {
                for (int i = 0; i < templateCount; i++)
                {
                    var templateName = $"TestTemplate_{i + 1:D2}";
                    var datData = GenerateTemplateDatData(templateName, i);
                    var filePath = Path.Combine(outputDirectory, $"{templateName}.dat");
                    
                    SaveDatFile(datData, filePath);
                    LogKit.I($"[{GetType().FullName}::GenerateTestTemplates] Generated: {filePath}");
                }
                
                LogKit.I($"[{GetType().FullName}::GenerateTestTemplates] Successfully generated {templateCount} test templates");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::GenerateTestTemplates] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 生成用户示例模版
        /// 基于用户需求中的示例：模版 1 2 3 4 5 6 7
        /// </summary>
        [ContextMenu("Generate User Example Template")]
        public void GenerateUserExampleTemplate()
        {
            try
            {
                var templateName = "UserExample_Template";
                var datData = GenerateUserExampleDatData();
                var filePath = Path.Combine(outputDirectory, $"{templateName}.dat");
                
                SaveDatFile(datData, filePath);
                LogKit.I($"[{GetType().FullName}::GenerateUserExampleTemplate] Generated user example template: {filePath}");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::GenerateUserExampleTemplate] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 生成模版dat数据
        /// </summary>
        private DatFileData GenerateTemplateDatData(string templateName, int templateIndex)
        {
            var datData = new DatFileData
            {
                gestureName = templateName,
                selectedJoints = new List<string> { "Head", "LeftHand", "RightHand" },
                movementData = new List<TimestampedMovementData>()
            };
            
            var startTime = DateTime.UtcNow;
            
            for (int i = 0; i < dataPointsPerTemplate; i++)
            {
                var timestamp = startTime.AddMilliseconds(i * timeIntervalMs);
                var movementData = GenerateMovementData(i, templateIndex);
                
                var timestampedData = new TimestampedMovementData
                {
                    timestamp = timestamp,
                    movementData = movementData
                };
                
                datData.movementData.Add(timestampedData);
            }
            
            return datData;
        }
        
        /// <summary>
        /// 生成用户示例dat数据
        /// 创建一个标准的7点序列模版
        /// </summary>
        private DatFileData GenerateUserExampleDatData()
        {
            var datData = new DatFileData
            {
                gestureName = "UserExample_1_2_3_4_5_6_7",
                selectedJoints = new List<string> { "Head", "LeftHand", "RightHand" },
                movementData = new List<TimestampedMovementData>()
            };
            
            var startTime = DateTime.UtcNow;
            
            // 生成序列 1 2 3 4 5 6 7 对应的运动数据
            for (int i = 0; i < 7; i++)
            {
                var timestamp = startTime.AddMilliseconds(i * timeIntervalMs);
                var movementData = GenerateSequentialMovementData(i + 1);
                
                var timestampedData = new TimestampedMovementData
                {
                    timestamp = timestamp,
                    movementData = movementData
                };
                
                datData.movementData.Add(timestampedData);
            }
            
            return datData;
        }
        
        /// <summary>
        /// 生成运动数据
        /// </summary>
        private MovementData GenerateMovementData(int step, int templateIndex)
        {
            var movementData = new MovementData();
            
            // 基于步骤和模版索引生成不同的运动模式
            var angle = (step * 60f + templateIndex * 120f) * Mathf.Deg2Rad;
            var radius = 1f + templateIndex * 0.5f;
            
            // 头部位置（圆形轨迹）
            var headPosition = new Vector3(
                Mathf.Cos(angle) * radius,
                Mathf.Sin(angle * 0.5f) * 0.5f,
                Mathf.Sin(angle) * radius
            );
            
            // 左手位置（椭圆轨迹）
            var leftHandPosition = new Vector3(
                Mathf.Cos(angle * 1.5f) * radius * 0.8f,
                Mathf.Sin(angle) * 0.3f,
                Mathf.Sin(angle * 1.2f) * radius * 0.6f
            );
            
            // 右手位置（8字轨迹）
            var rightHandPosition = new Vector3(
                Mathf.Sin(angle * 2f) * radius * 0.7f,
                Mathf.Cos(angle * 1.5f) * 0.4f,
                Mathf.Cos(angle) * radius * 0.5f
            );
            
            // 设置关节数据
            movementData.SetJointPosition("Head", headPosition);
            movementData.SetJointPosition("LeftHand", leftHandPosition);
            movementData.SetJointPosition("RightHand", rightHandPosition);
            
            // 设置旋转（基于位置变化）
            var headRotation = Quaternion.Euler(angle * Mathf.Rad2Deg * 0.1f, angle * Mathf.Rad2Deg * 0.2f, 0);
            movementData.SetJointRotation("Head", headRotation);
            
            return movementData;
        }
        
        /// <summary>
        /// 生成序列化运动数据
        /// 为用户示例生成更规律的数据
        /// </summary>
        private MovementData GenerateSequentialMovementData(int sequenceNumber)
        {
            var movementData = new MovementData();
            
            // 基于序列号生成线性变化的运动
            var t = (sequenceNumber - 1) / 6f; // 0 to 1
            
            // 头部：线性移动
            var headPosition = Vector3.Lerp(
                new Vector3(-1f, 0f, 0f),
                new Vector3(1f, 0f, 0f),
                t
            );
            
            // 左手：弧形移动
            var leftHandPosition = new Vector3(
                Mathf.Lerp(-0.8f, 0.8f, t),
                Mathf.Sin(t * Mathf.PI) * 0.5f,
                0f
            );
            
            // 右手：螺旋移动
            var rightHandPosition = new Vector3(
                Mathf.Cos(t * Mathf.PI * 2f) * 0.6f,
                t * 0.8f - 0.4f,
                Mathf.Sin(t * Mathf.PI * 2f) * 0.6f
            );
            
            movementData.SetJointPosition("Head", headPosition);
            movementData.SetJointPosition("LeftHand", leftHandPosition);
            movementData.SetJointPosition("RightHand", rightHandPosition);
            
            // 简单的旋转
            var rotation = Quaternion.Euler(0, t * 180f, 0);
            movementData.SetJointRotation("Head", rotation);
            
            return movementData;
        }
        
        /// <summary>
        /// 保存dat文件
        /// </summary>
        private void SaveDatFile(DatFileData datData, string filePath)
        {
            try
            {
                var json = JsonUtility.ToJson(datData, true);
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to save dat file {filePath}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 生成测试输入序列
        /// 基于用户示例生成MovementData序列
        /// </summary>
        [ContextMenu("Generate Test Input Sequences")]
        public void GenerateTestInputSequences()
        {
            try
            {
                // 生成用户示例中的4个输入序列
                GenerateInputSequence("Input1_LowSimilarity", new List<int> { 1, 1, 2, 3, 4, 5, 4, 3, 2 });
                GenerateInputSequence("Input2_HighSimilarity", new List<int> { 1, 2, 1, 1, 2, 3, 4, 5, 6, 7 });
                GenerateInputSequence("Input3_PerfectMatch", new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 1, 2 });
                GenerateInputSequence("Input4_LowSimilarity", new List<int> { 3, 2, 5, 3, 1, 2, 3, 4, 1, 1, 1 });
                
                LogKit.I($"[{GetType().FullName}::GenerateTestInputSequences] Generated test input sequences");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::GenerateTestInputSequences] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 生成单个输入序列
        /// </summary>
        private void GenerateInputSequence(string sequenceName, List<int> sequence)
        {
            var movementDataList = new List<TimestampedMovementData>();
            var startTime = DateTime.UtcNow;
            
            for (int i = 0; i < sequence.Count; i++)
            {
                var timestamp = startTime.AddMilliseconds(i * timeIntervalMs);
                var movementData = GenerateSequentialMovementData(sequence[i]);
                
                var timestampedData = new TimestampedMovementData
                {
                    timestamp = timestamp,
                    movementData = movementData
                };
                
                movementDataList.Add(timestampedData);
            }
            
            // 保存为JSON文件（用于测试）
            var filePath = Path.Combine(outputDirectory, $"{sequenceName}.json");
            var json = JsonUtility.ToJson(new { sequence = sequence, movementData = movementDataList }, true);
            File.WriteAllText(filePath, json);
            
            LogKit.I($"[{GetType().FullName}::GenerateInputSequence] Generated: {filePath}");
        }
        
        /// <summary>
        /// 清理测试数据
        /// </summary>
        [ContextMenu("Clean Test Data")]
        public void CleanTestData()
        {
            try
            {
                if (Directory.Exists(outputDirectory))
                {
                    var files = Directory.GetFiles(outputDirectory, "*.*");
                    foreach (var file in files)
                    {
                        File.Delete(file);
                    }
                    LogKit.I($"[{GetType().FullName}::CleanTestData] Cleaned {files.Length} files from {outputDirectory}");
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::CleanTestData] Error: {ex.Message}");
            }
        }
    }
}
