using System;
using System.Collections.Generic;
using UnityEngine;
using GameShare.Events;
using TFGShare.Utility;

namespace TFGClient.Gesture.Tests
{
    /// <summary>
    /// 连续识别功能测试脚本
    /// 验证用户需求中的滑动窗口匹配效果
    /// </summary>
    public class ContinuousRecognitionTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [Tooltip("启用自动测试")]
        public bool enableAutoTest = true;
        
        [Tooltip("测试间隔时间（秒）")]
        public float testInterval = 2.0f;
        
        [Tooltip("启用详细日志")]
        public bool enableDetailedLogging = true;
        
        [Header("Test Data")]
        [Tooltip("模版数据长度")]
        public int templateLength = 7;
        
        [Tooltip("测试输入序列")]
        public List<TestInputSequence> testSequences = new List<TestInputSequence>();
        
        // 测试结果记录
        private List<TestResult> _testResults = new List<TestResult>();
        private float _lastTestTime = 0f;
        private int _currentTestIndex = 0;
        
        [System.Serializable]
        public class TestInputSequence
        {
            public string name;
            public List<int> sequence;
            public float expectedSimilarity;
            public string description;
        }
        
        [System.Serializable]
        public class TestResult
        {
            public string testName;
            public float actualSimilarity;
            public float expectedSimilarity;
            public bool passed;
            public float executionTime;
            public string details;
        }
        
        private void Start()
        {
            InitializeTestData();
            LogKit.I($"[{GetType().FullName}::Start] Continuous Recognition Test initialized with {testSequences.Count} test cases");
        }
        
        private void Update()
        {
            if (enableAutoTest && Time.time - _lastTestTime >= testInterval)
            {
                RunNextTest();
                _lastTestTime = Time.time;
            }
        }
        
        /// <summary>
        /// 初始化测试数据
        /// 基于用户需求示例创建测试用例
        /// </summary>
        private void InitializeTestData()
        {
            testSequences.Clear();
            
            // 用户示例：模版 1 2 3 4 5 6 7
            // 输入1：1 1 2 3 4 5 4 3 2 - 相似度很低
            testSequences.Add(new TestInputSequence
            {
                name = "Input1_LowSimilarity",
                sequence = new List<int> { 1, 1, 2, 3, 4, 5, 4, 3, 2 },
                expectedSimilarity = 0.3f,
                description = "长度9，包含重复和反向，期望相似度很低"
            });
            
            // 输入2：1 2 1 1 2 3 4 5 6 7 - 相似度较高
            testSequences.Add(new TestInputSequence
            {
                name = "Input2_HighSimilarity",
                sequence = new List<int> { 1, 2, 1, 1, 2, 3, 4, 5, 6, 7 },
                expectedSimilarity = 0.7f,
                description = "长度10，后半部分匹配模版，期望相似度较高"
            });
            
            // 输入3：1 2 3 4 5 6 7 8 1 2 - 相似度100%
            testSequences.Add(new TestInputSequence
            {
                name = "Input3_PerfectMatch",
                sequence = new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 1, 2 },
                expectedSimilarity = 0.95f,
                description = "长度10，前7个完全匹配模版，期望相似度100%"
            });
            
            // 输入4：3 2 5 3 1 2 3 4 1 1 1 - 相似度不高
            testSequences.Add(new TestInputSequence
            {
                name = "Input4_LowSimilarity",
                sequence = new List<int> { 3, 2, 5, 3, 1, 2, 3, 4, 1, 1, 1 },
                expectedSimilarity = 0.4f,
                description = "长度11，乱序数据，期望相似度不高"
            });
            
            // 额外测试：完全匹配
            testSequences.Add(new TestInputSequence
            {
                name = "ExactMatch",
                sequence = new List<int> { 1, 2, 3, 4, 5, 6, 7 },
                expectedSimilarity = 1.0f,
                description = "长度7，完全匹配模版"
            });
            
            // 额外测试：部分匹配
            testSequences.Add(new TestInputSequence
            {
                name = "PartialMatch",
                sequence = new List<int> { 0, 0, 1, 2, 3, 4, 5, 6, 7, 0, 0 },
                expectedSimilarity = 0.8f,
                description = "长度11，中间部分完全匹配"
            });
        }
        
        /// <summary>
        /// 运行下一个测试用例
        /// </summary>
        private void RunNextTest()
        {
            if (_currentTestIndex >= testSequences.Count)
            {
                _currentTestIndex = 0;
                PrintTestSummary();
                return;
            }
            
            var testCase = testSequences[_currentTestIndex];
            var startTime = Time.realtimeSinceStartup;
            
            try
            {
                // 模拟测试（实际应该调用识别系统）
                var actualSimilarity = SimulateRecognition(testCase.sequence);
                var executionTime = (Time.realtimeSinceStartup - startTime) * 1000f; // ms
                
                var result = new TestResult
                {
                    testName = testCase.name,
                    actualSimilarity = actualSimilarity,
                    expectedSimilarity = testCase.expectedSimilarity,
                    passed = Mathf.Abs(actualSimilarity - testCase.expectedSimilarity) <= 0.2f,
                    executionTime = executionTime,
                    details = testCase.description
                };
                
                _testResults.Add(result);
                
                if (enableDetailedLogging)
                {
                    LogTestResult(result);
                }
                
                _currentTestIndex++;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::RunNextTest] Test {testCase.name} failed: {ex.Message}");
                _currentTestIndex++;
            }
        }
        
        /// <summary>
        /// 模拟识别过程
        /// 实际使用时应该调用真实的识别系统
        /// </summary>
        /// <param name="inputSequence">输入序列</param>
        /// <returns>相似度</returns>
        private float SimulateRecognition(List<int> inputSequence)
        {
            // 模版序列：1 2 3 4 5 6 7
            var template = new List<int> { 1, 2, 3, 4, 5, 6, 7 };
            
            // 简单的滑动窗口匹配模拟
            float bestSimilarity = 0f;
            
            if (inputSequence.Count < template.Count)
            {
                // 输入序列太短，直接比较
                return CalculateSimpleSimilarity(template, inputSequence);
            }
            
            // 滑动窗口匹配
            for (int i = 0; i <= inputSequence.Count - template.Count; i++)
            {
                var window = inputSequence.GetRange(i, template.Count);
                var similarity = CalculateSimpleSimilarity(template, window);
                
                if (similarity > bestSimilarity)
                {
                    bestSimilarity = similarity;
                }
                
                if (enableDetailedLogging)
                {
                    LogKit.I($"[{GetType().FullName}::SimulateRecognition] Window {i}: {string.Join(",", window)} -> Similarity: {similarity:F3}");
                }
            }
            
            return bestSimilarity;
        }
        
        /// <summary>
        /// 计算简单相似度
        /// </summary>
        private float CalculateSimpleSimilarity(List<int> template, List<int> input)
        {
            if (template.Count != input.Count) return 0f;
            
            int matches = 0;
            for (int i = 0; i < template.Count; i++)
            {
                if (template[i] == input[i])
                {
                    matches++;
                }
            }
            
            return (float)matches / template.Count;
        }
        
        /// <summary>
        /// 记录测试结果
        /// </summary>
        private void LogTestResult(TestResult result)
        {
            var status = result.passed ? "PASS" : "FAIL";
            var color = result.passed ? "green" : "red";
            
            LogKit.I($"[{GetType().FullName}::Test] <color={color}>{status}</color> {result.testName}: " +
                    $"Expected: {result.expectedSimilarity:F2}, Actual: {result.actualSimilarity:F2}, " +
                    $"Time: {result.executionTime:F1}ms");
        }
        
        /// <summary>
        /// 打印测试总结
        /// </summary>
        private void PrintTestSummary()
        {
            if (_testResults.Count == 0) return;
            
            int passCount = 0;
            float totalTime = 0f;
            
            foreach (var result in _testResults)
            {
                if (result.passed) passCount++;
                totalTime += result.executionTime;
            }
            
            var passRate = (float)passCount / _testResults.Count * 100f;
            var avgTime = totalTime / _testResults.Count;
            
            LogKit.I($"[{GetType().FullName}::TestSummary] " +
                    $"Tests: {_testResults.Count}, Passed: {passCount}, Pass Rate: {passRate:F1}%, " +
                    $"Avg Time: {avgTime:F1}ms");
            
            _testResults.Clear();
        }
        
        /// <summary>
        /// 手动运行所有测试
        /// </summary>
        [ContextMenu("Run All Tests")]
        public void RunAllTests()
        {
            _currentTestIndex = 0;
            _testResults.Clear();
            
            for (int i = 0; i < testSequences.Count; i++)
            {
                RunNextTest();
            }
            
            PrintTestSummary();
        }
        
        /// <summary>
        /// 重置测试
        /// </summary>
        [ContextMenu("Reset Tests")]
        public void ResetTests()
        {
            _currentTestIndex = 0;
            _testResults.Clear();
            InitializeTestData();
            LogKit.I($"[{GetType().FullName}::ResetTests] Tests reset");
        }
    }
}
