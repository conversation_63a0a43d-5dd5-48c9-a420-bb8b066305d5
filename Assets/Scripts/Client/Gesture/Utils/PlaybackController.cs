using System;
using System.Collections.Generic;
using System.IO;
using QFramework;
using TFGShare.Protocol;
using TFGShare.Utility;
using UnityEngine;

namespace GameClient.Gesture
{
    /// <summary>
    /// 回放控制器，负责按时间戳回放MovementData并驱动avatar
    /// </summary>
    public class PlaybackController
    {
        private List<TimestampedMovementData> _playbackData;
        private float _playbackStartTime;
        private int _currentIndex;
        private bool _isPlaying;
        private long _baseTimestamp;
        
        /// <summary>
        /// 是否正在回放
        /// </summary>
        public bool IsPlaying => _isPlaying;
        
        /// <summary>
        /// 回放进度（0-1）
        /// </summary>
        public float Progress
        {
            get
            {
                if (_playbackData == null || _playbackData.Count == 0)
                    return 0f;
                return (float)_currentIndex / _playbackData.Count;
            }
        }
        
        /// <summary>
        /// 回放开始事件
        /// </summary>
        public event Action OnPlaybackStarted;
        
        /// <summary>
        /// 回放结束事件
        /// </summary>
        public event Action OnPlaybackFinished;
        
        /// <summary>
        /// 回放进度更新事件
        /// </summary>
        public event Action<float> OnProgressUpdated;
        
        /// <summary>
        /// 从json文件开始回放
        /// </summary>
        /// <param name="jsonFilePath">json文件路径</param>
        /// <returns>是否成功开始回放</returns>
        public bool StartPlayback(string jsonFilePath)
        {
            try
            {
                if (string.IsNullOrEmpty(jsonFilePath) || !File.Exists(jsonFilePath))
                {
                    LogKit.E($"[{GetType().FullName}::StartPlayback] File not found: {jsonFilePath}");
                    return false;
                }
                
                // 加载json数据
                var jsonContent = File.ReadAllText(jsonFilePath);
                _playbackData = LoadTimestampedDataFromJson(jsonContent);
                
                if (_playbackData == null || _playbackData.Count == 0)
                {
                    LogKit.E($"[{GetType().FullName}::StartPlayback] No valid data in file: {jsonFilePath}");
                    return false;
                }

                // 初始化回放状态
                _playbackStartTime = Time.time;
                _currentIndex = 0;
                _isPlaying = true;
                _baseTimestamp = _playbackData[0].TimestampMs;

                OnPlaybackStarted?.Invoke();
                LogKit.I($"[{GetType().FullName}::StartPlayback] Started playback with {_playbackData.Count} frames");
                
                return true;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::StartPlayback] Error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 停止回放
        /// </summary>
        public void StopPlayback()
        {
            try
            {
                if (!_isPlaying)
                    return;
                
                _isPlaying = false;
                _playbackData = null;
                _currentIndex = 0;
                
                OnPlaybackFinished?.Invoke();
                LogKit.I($"[{GetType().FullName}::StopPlayback] Playback stopped");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::StopPlayback] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新回放（在Update中调用）
        /// </summary>
        public void UpdatePlayback()
        {
            try
            {
                if (!_isPlaying || _playbackData == null || _currentIndex >= _playbackData.Count)
                {
                    if (_isPlaying && _currentIndex >= _playbackData.Count)
                    {
                        // 回放完成
                        StopPlayback();
                    }
                    return;
                }
                
                var currentTime = Time.time;
                var elapsedTime = (currentTime - _playbackStartTime) * 1000; // 转换为毫秒
                
                // 检查是否到了播放下一帧的时间
                while (_currentIndex < _playbackData.Count)
                {
                    var targetTime = _playbackData[_currentIndex].TimestampMs - _baseTimestamp;
                    
                    if (elapsedTime >= targetTime)
                    {
                        // 驱动avatar
                        DriveAvatar(_playbackData[_currentIndex].MovementData);
                        
                        _currentIndex++;
                        
                        // 更新进度
                        OnProgressUpdated?.Invoke(Progress);
                    }
                    else
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::UpdatePlayback] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 驱动avatar（空函数，用户可以补充实现）
        /// </summary>
        /// <param name="movementData">运动数据</param>
        protected virtual void DriveAvatar(MovementData movementData)
        {
            // 空函数实现，用户可以在这里添加avatar驱动逻辑
            // 例如：
            // - 设置avatar的关节位置和旋转
            // - 更新动画状态
            // - 触发特效等
            
            // 示例代码（注释掉，用户可以参考）：
            /*
            if (movementData?.Datas != null)
            {
                for (int i = 0; i < movementData.Datas.Length; i++)
                {
                    var jointData = movementData.Datas[i];
                    var position = new Vector3(jointData.Pos.x, jointData.Pos.y, jointData.Pos.z);
                    var rotation = new Quaternion(jointData.Rot.x, jointData.Rot.y, jointData.Rot.z, jointData.Rot.w);
                    
                    // 设置对应关节的位置和旋转
                    // SetJointTransform(i, position, rotation);
                }
            }
            */
        }
        
        /// <summary>
        /// 从json内容加载TimestampedMovementData列表
        /// </summary>
        /// <param name="jsonContent">json内容</param>
        /// <returns>TimestampedMovementData列表</returns>
        private List<TimestampedMovementData> LoadTimestampedDataFromJson(string jsonContent)
        {
            try
            {
                // 这里需要根据实际的json格式来解析
                // 假设json格式为TimestampedMovementData的数组
                
                // 简单的解析示例（用户需要根据实际格式调整）
                var wrapper = JsonUtility.FromJson<TimestampedDataWrapper>($"{{\"data\":{jsonContent}}}");
                return wrapper?.data ?? new List<TimestampedMovementData>();
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::LoadTimestampedDataFromJson] Error: {ex.Message}");
                return new List<TimestampedMovementData>();
            }
        }
        
        /// <summary>
        /// 获取当前回放的数据（用于保存模版）
        /// </summary>
        /// <returns>当前回放的数据列表</returns>
        public List<TimestampedMovementData> GetCurrentPlaybackData()
        {
            return _playbackData;
        }
        
        /// <summary>
        /// json数据包装器（用于解析）
        /// </summary>
        [Serializable]
        private class TimestampedDataWrapper
        {
            public List<TimestampedMovementData> data;
        }
    }
}
