using System;
using System.Collections.Generic;
using System.IO;
using QFramework;
using TFGShare.Utility;
using UnityEngine;

namespace GameClient.Gesture
{
    /// <summary>
    /// dat文件管理器，专注于dat文件的保存和加载
    /// 格式：名字、是否开启头部朝向、List（部位，curve）
    /// </summary>
    public static class DatFileManager
    {
        // 性能优化：curves缓存，避免重复构建
        private static readonly Dictionary<string, Dictionary<string, List<Vector3>>> _curvesCache = new();
        private static readonly Dictionary<string, DateTime> _fileModifyTimes = new();
        /// <summary>
        /// dat文件数据结构
        /// </summary>
        [Serializable]
        public class DatFileData
        {
            public string name;                                    // 名字
            public bool useHeadOrientation;                        // 是否开启头部朝向
            public List<PartCurve> partCurves = new();            // List（部位，curve）
        }
        
        /// <summary>
        /// 部位曲线数据
        /// </summary>
        [Serializable]
        public class PartCurve
        {
            public string partName;                               // 部位名称
            public List<Vector3> curve = new();                  // 曲线数据
        }
        
        /// <summary>
        /// 保存dat文件
        /// </summary>
        /// <param name="name">名字</param>
        /// <param name="recordedSequence">录制的序列</param>
        /// <param name="selectedJoints">选择的关节点</param>
        /// <param name="useHeadOrientation">是否开启头部朝向</param>
        /// <returns>保存的文件路径</returns>
        public static string SaveDatFile(string name, List<TimestampedMovementData> recordedSequence, List<TrajectoryExtractor.JointIndex> selectedJoints, bool useHeadOrientation = true)
        {
            try
            {
                if (string.IsNullOrEmpty(name) || recordedSequence == null || 
                    recordedSequence.Count == 0 || selectedJoints == null || selectedJoints.Count == 0)
                {
                    LogKit.E($"[{typeof(DatFileManager).FullName}::SaveDatFile] Invalid input parameters");
                    return string.Empty;
                }
                
                var trajectoryExtractor = new TrajectoryExtractor();
                var datData = new DatFileData
                {
                    name = name,
                    useHeadOrientation = useHeadOrientation
                };
                
                // 提取每个部位的曲线
                foreach (var joint in selectedJoints)
                {
                    var trajectoryCurve = trajectoryExtractor.ExtractJointTrajectory(
                        recordedSequence, joint, useHeadOrientation);
                    
                    // 应用滤波（防止尖刺）
                    var filteredCurve = CurveMatcher.RemoveSpikes(trajectoryCurve.Points);
                    filteredCurve = CurveMatcher.FilterCurve(filteredCurve);
                    
                    datData.partCurves.Add(new PartCurve
                    {
                        partName = joint.ToString(),
                        curve = filteredCurve
                    });
                }
                
                // 保存为dat文件
                var filePath = SaveToFile(datData);
                
                LogKit.I($"[{typeof(DatFileManager).FullName}::SaveDatFile] Saved: {name} to {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{typeof(DatFileManager).FullName}::SaveDatFile] Error: {ex.Message}");
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 加载所有dat文件
        /// </summary>
        /// <returns>dat文件数据字典</returns>
        public static Dictionary<string, DatFileData> LoadAllDatFiles()
        {
            var datFiles = new Dictionary<string, DatFileData>();
            
            try
            {
                var datDir = Path.Combine(Application.dataPath, "GestureData");
                if (!Directory.Exists(datDir))
                {
                    LogKit.W($"[{typeof(DatFileManager).FullName}::LoadAllDatFiles] Data directory not found");
                    return datFiles;
                }
                
                var files = Directory.GetFiles(datDir, "*.dat");
                
                foreach (var filePath in files)
                {
                    try
                    {
                        var datData = LoadFromFile(filePath);
                        if (datData != null && !string.IsNullOrEmpty(datData.name))
                        {
                            var fileName = Path.GetFileNameWithoutExtension(filePath);
                            datFiles[fileName] = datData;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogKit.E($"[{typeof(DatFileManager).FullName}::LoadAllDatFiles] Error loading {filePath}: {ex.Message}");
                    }
                }
                
                LogKit.I($"[{typeof(DatFileManager).FullName}::LoadAllDatFiles] Loaded {datFiles.Count} dat files");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{typeof(DatFileManager).FullName}::LoadAllDatFiles] Error: {ex.Message}");
            }
            
            return datFiles;
        }
        
        /// <summary>
        /// 匹配实时行为与dat数据（性能优化版）
        /// </summary>
        /// <param name="datData">dat文件数据</param>
        /// <param name="inputSequence">实时输入序列</param>
        /// <param name="enableDetailedLogging">启用详细日志</param>
        /// <param name="threshold">识别阈值</param>
        /// <returns>相似度</returns>
        public static float MatchWithDatData(DatFileData datData, List<TimestampedMovementData> inputSequence, bool enableDetailedLogging = false, float threshold = 0.8f)
        {
            try
            {
                if (datData?.partCurves == null || datData.partCurves.Count == 0 ||
                    inputSequence == null || inputSequence.Count == 0)
                {
                    return 0f;
                }

                // 性能优化：使用缓存的模板curves
                var templateCurves = GetCachedTemplateCurves(datData);

                var trajectoryExtractor = new TrajectoryExtractor();
                var inputCurves = new Dictionary<string, List<Vector3>>();

                // 提取输入序列的曲线
                foreach (var partCurve in datData.partCurves)
                {
                    if (Enum.TryParse<TrajectoryExtractor.JointIndex>(partCurve.partName, out var jointIndex))
                    {
                        var inputCurve = trajectoryExtractor.ExtractJointTrajectory(
                            inputSequence, jointIndex, datData.useHeadOrientation);

                        // 应用滤波
                        var filteredInputCurve = CurveMatcher.RemoveSpikes(inputCurve.Points);
                        filteredInputCurve = CurveMatcher.FilterCurve(filteredInputCurve);

                        inputCurves[partCurve.partName] = filteredInputCurve;
                    }
                }

                // 匹配曲线
                return CurveMatcher.MatchMultipleCurves(templateCurves, inputCurves, threshold, enableDetailedLogging);
            }
            catch (Exception ex)
            {
                LogKit.E($"[{typeof(DatFileManager).FullName}::MatchWithDatData] Error: {ex.Message}");
                return 0f;
            }
        }

        /// <summary>
        /// 获取缓存的模板curves（性能优化）
        /// </summary>
        private static Dictionary<string, List<Vector3>> GetCachedTemplateCurves(DatFileData datData)
        {
            var cacheKey = datData.name;

            // 检查缓存是否存在且有效
            if (_curvesCache.TryGetValue(cacheKey, out var cachedCurves))
            {
                return cachedCurves;
            }

            // 构建并缓存curves（性能优化：避免重复构建）
            var templateCurves = new Dictionary<string, List<Vector3>>();
            foreach (var partCurve in datData.partCurves)
            {
                // 直接引用原始curve数据，避免复制
                templateCurves[partCurve.partName] = partCurve.curve;
            }

            _curvesCache[cacheKey] = templateCurves;
            LogKit.I($"[{typeof(DatFileManager).FullName}::GetCachedTemplateCurves] Cached curves for: {cacheKey}");
            return templateCurves;
        }

        /// <summary>
        /// 清理curves缓存
        /// </summary>
        public static void ClearCache()
        {
            _curvesCache.Clear();
            _fileModifyTimes.Clear();
        }
        
        /// <summary>
        /// 保存到文件
        /// </summary>
        private static string SaveToFile(DatFileData datData)
        {
            try
            {
                var datDir = Path.Combine(Application.dataPath, "GestureData");
                if (!Directory.Exists(datDir))
                {
                    Directory.CreateDirectory(datDir);
                }
                
                var fileName = $"{datData.name}_{DateTime.Now:yyyyMMddHHmmss}.dat";
                var filePath = Path.Combine(datDir, fileName);
                
                var json = JsonUtility.ToJson(datData, true);
                File.WriteAllText(filePath, json);
                
                return filePath;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{typeof(DatFileManager).FullName}::SaveToFile] Error: {ex.Message}");
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 从文件加载
        /// </summary>
        private static DatFileData LoadFromFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return null;
                }
                
                var json = File.ReadAllText(filePath);
                return JsonUtility.FromJson<DatFileData>(json);
            }
            catch (Exception ex)
            {
                LogKit.E($"[{typeof(DatFileManager).FullName}::LoadFromFile] Error: {ex.Message}");
                return null;
            }
        }
    }
}
