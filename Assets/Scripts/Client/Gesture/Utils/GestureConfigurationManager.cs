using System;
using System.Collections.Generic;
using System.IO;
using QFramework;
using UnityEngine;
using UnityEngine.UI;

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势配置管理工具类，负责关节点选择和配置生成
    /// </summary>
    public class GestureConfigurationManager
    {
        private readonly Dictionary<HumanBodyBones, Toggle> _dictBoneToggles = new();
        private readonly JointFocusConfig _currentJointConfig = new();
        
        /// <summary>
        /// 当前关节配置
        /// </summary>
        public JointFocusConfig CurrentJointConfig => _currentJointConfig;
        
        /// <summary>
        /// 初始化关节点选择
        /// </summary>
        /// <param name="jointSelectionContainer">关节选择容器</param>
        public void InitializeJointSelection(RectTransform jointSelectionContainer)
        {
            try
            {
                if (jointSelectionContainer == null)
                {
                    LogKit.W($"[{GetType().FullName}::InitializeJointSelection] Joint selection container not found");
                    return;
                }
                jointSelectionContainer.gameObject.SetActive(false);
                // 清空现有的 Toggle
                _dictBoneToggles.Clear();
                
                // 获取容器中的所有 Toggle 组件
                var toggles = jointSelectionContainer.GetComponentsInChildren<Toggle>();
                
                // 根据 Toggle 的名称映射到对应的关节
                foreach (var toggle in toggles)
                {
                    if (Enum.TryParse<HumanBodyBones>(toggle.name, out var bone))
                    {
                        _dictBoneToggles[bone] = toggle;
                        
                        // 绑定事件
                        toggle.onValueChanged.RemoveAllListeners();
                        toggle.onValueChanged.AddListener((isOn) => OnJointToggleChanged(bone, isOn));
                    }
                }
                
                // 初始化默认配置
                InitializeDefaultJointConfig();
                
                LogKit.I($"[{GetType().FullName}::InitializeJointSelection] Initialized {_dictBoneToggles.Count} joint toggles");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::InitializeJointSelection] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 初始化默认关节配置（支持五个关键关节点）
        /// </summary>
        private void InitializeDefaultJointConfig()
        {
            try
            {
                // 设置默认关注的五个关键关节点（符合用户需求）
                var defaultJoints = new[]
                {
                    HumanBodyBones.Head,        // 头部
                    HumanBodyBones.LeftHand,    // 左手
                    HumanBodyBones.RightHand,   // 右手
                    HumanBodyBones.LeftFoot,    // 左脚
                    HumanBodyBones.RightFoot    // 右脚
                };

                _currentJointConfig.RotationJoints.Clear();

                foreach (var joint in defaultJoints)
                {
                    if (_dictBoneToggles.TryGetValue(joint, out var toggle))
                    {
                        // 先设置配置，再设置UI状态，避免事件触发时配置不一致
                        _currentJointConfig.RotationJoints.Add(joint);

                        // 设置Toggle状态并强制刷新UI
                        toggle.isOn = true;

                        // 确保UI状态同步，强制触发一次事件以确保状态一致
                        toggle.onValueChanged?.Invoke(true);

                        LogKit.I($"[{GetType().FullName}::InitializeDefaultJointConfig] Enabled joint: {joint}, Toggle state: {toggle.isOn}");
                    }
                    else
                    {
                        // 如果Toggle不存在，仍然添加到配置中（支持代码配置）
                        _currentJointConfig.RotationJoints.Add(joint);
                        LogKit.W($"[{GetType().FullName}::InitializeDefaultJointConfig] Joint {joint} added to config but no Toggle found");
                    }
                }

                LogKit.I($"[{GetType().FullName}::InitializeDefaultJointConfig] Initialized {_currentJointConfig.RotationJoints.Count} default joints");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::InitializeDefaultJointConfig] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 关节点 Toggle 变化事件处理
        /// </summary>
        /// <param name="bone">关节点</param>
        /// <param name="isOn">是否选中</param>
        private void OnJointToggleChanged(HumanBodyBones bone, bool isOn)
        {
            try
            {
                if (isOn)
                {
                    if (!_currentJointConfig.RotationJoints.Contains(bone))
                    {
                        _currentJointConfig.RotationJoints.Add(bone);
                    }
                }
                else
                {
                    _currentJointConfig.RotationJoints.Remove(bone);
                }
                
                LogKit.I($"[{GetType().FullName}::OnJointToggleChanged] Joint {bone} set to {isOn}");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnJointToggleChanged] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <returns>保存的文件名</returns>
        public string SaveConfiguration()
        {
            try
            {
                // 保存当前的关节配置
                var configJson = JsonUtility.ToJson(_currentJointConfig, true);
                var configPath = Path.Combine(UnityEngine.Application.dataPath, "GestureConfigs");
                
                if (!Directory.Exists(configPath))
                {
                    Directory.CreateDirectory(configPath);
                }
                
                var fileName = $"GestureConfig_{DateTime.Now:yyyyMMddHHmmss}.json";
                var filePath = Path.Combine(configPath, fileName);
                
                File.WriteAllText(filePath, configJson);
                
                LogKit.I($"[{GetType().FullName}::SaveConfiguration] Configuration saved to: {fileName}");

                return fileName;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::SaveConfiguration] Error: {ex.Message}");
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 检查关节是否被选中
        /// </summary>
        /// <param name="jointName">关节名称</param>
        /// <returns>是否选中</returns>
        public bool IsJointSelected(string jointName)
        {
            // 将关节名称转换为 HumanBodyBones 枚举
            if (Enum.TryParse<HumanBodyBones>(jointName, out var bone))
            {
                return _currentJointConfig.RotationJoints.Contains(bone);
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取配置状态文本
        /// </summary>
        /// <returns>状态文本</returns>
        public string GetConfigurationStatusText()
        {
            var selectedJoints = _currentJointConfig.RotationJoints.Count;
            var distanceConfigs = _currentJointConfig.DistanceConfigs.Count;
            return $"Selected Joints: {selectedJoints}, Distance Configs: {distanceConfigs}";
        }

        /// <summary>
        /// 获取选择的关节点（转换为TrajectoryExtractor.JointIndex）
        /// </summary>
        /// <returns>选择的关节点列表</returns>
        public List<TrajectoryExtractor.JointIndex> GetSelectedTrajectoryJoints()
        {
            var selectedJoints = new List<TrajectoryExtractor.JointIndex>();

            try
            {
                foreach (var bone in _currentJointConfig.RotationJoints)
                {
                    var trajectoryJoint = ConvertToTrajectoryJoint(bone);
                    if (trajectoryJoint.HasValue)
                    {
                        selectedJoints.Add(trajectoryJoint.Value);
                    }
                }

                LogKit.I($"[{GetType().FullName}::GetSelectedTrajectoryJoints] Converted {selectedJoints.Count} joints");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::GetSelectedTrajectoryJoints] Error: {ex.Message}");
            }

            return selectedJoints;
        }

        /// <summary>
        /// 将HumanBodyBones转换为TrajectoryExtractor.JointIndex
        /// </summary>
        /// <param name="bone">人体骨骼枚举</param>
        /// <returns>轨迹关节索引，如果无法转换则返回null</returns>
        private TrajectoryExtractor.JointIndex? ConvertToTrajectoryJoint(HumanBodyBones bone)
        {
            return bone switch
            {
                HumanBodyBones.Head => TrajectoryExtractor.JointIndex.Head,
                HumanBodyBones.LeftHand => TrajectoryExtractor.JointIndex.LeftHand,
                HumanBodyBones.RightHand => TrajectoryExtractor.JointIndex.RightHand,
                HumanBodyBones.LeftFoot => TrajectoryExtractor.JointIndex.LeftFoot,
                HumanBodyBones.RightFoot => TrajectoryExtractor.JointIndex.RightFoot,
                _ => null
            };
        }
    }
}
