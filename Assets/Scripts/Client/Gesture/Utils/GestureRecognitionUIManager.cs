using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using QFramework;
using TMPro;
using UnityEngine;

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势识别UI管理工具类，负责相似度显示和计时器管理
    /// </summary>
    public class GestureRecognitionUIManager
    {
        private readonly Dictionary<string, float> _displayTimers = new(); // 相似度保持显示计时器
        private readonly Dictionary<string, float> _currentSimilarities = new();
        private readonly Dictionary<string, GameObject> _templateListItems = new();
        
        /// <summary>
        /// 创建模板列表项
        /// </summary>
        /// <param name="templateListContainer">模板列表容器</param>
        public void CreateTemplateListItems(RectTransform templateListContainer)
        {
            try
            {
                if (templateListContainer == null)
                {
                    return;
                }
                
                // 清空现有项目
                foreach (var item in _templateListItems.Values)
                {
                    if (item != null)
                    {
                        UnityEngine.Object.DestroyImmediate(item);
                    }
                }
                _templateListItems.Clear();
                
                // 从 GestureTemplates 目录加载模板
                var templatesPath = Path.Combine(UnityEngine.Application.dataPath, "GestureTemplates");
                if (Directory.Exists(templatesPath))
                {
                    var jsonFiles = Directory.GetFiles(templatesPath, "*.json");
                    
                    foreach (var file in jsonFiles)
                    {
                        var fileName = Path.GetFileNameWithoutExtension(file);
                        CreateTemplateListItem(templateListContainer, fileName);
                    }
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[GestureRecognitionUIManager::CreateTemplateListItems] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 创建单个模板列表项
        /// </summary>
        /// <param name="templateListContainer">模板列表容器</param>
        /// <param name="templateName">模板名称</param>
        private void CreateTemplateListItem(RectTransform templateListContainer, string templateName)
        {
            try
            {
                if (templateListContainer == null)
                {
                    return;
                }
                
                // 创建简单的文本项目
                var itemGO = new GameObject($"Template_{templateName}");
                itemGO.transform.SetParent(templateListContainer, false);
                
                var textComponent = itemGO.AddComponent<TextMeshProUGUI>();
                textComponent.text = $"{templateName}: 0.00";
                textComponent.fontSize = 14;
                
                _templateListItems[templateName] = itemGO;
            }
            catch (Exception ex)
            {
                LogKit.E($"[GestureRecognitionUIManager::CreateTemplateListItem] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新相似度显示
        /// </summary>
        /// <param name="gestureName">手势名称</param>
        /// <param name="similarity">相似度</param>
        /// <param name="isHighlighted">是否高亮显示</param>
        public void UpdateSimilarityDisplay(string gestureName, float similarity, bool isHighlighted)
        {
            try
            {
                if (_templateListItems.TryGetValue(gestureName, out var itemGO) && itemGO != null)
                {
                    var textComponent = itemGO.GetComponent<TextMeshProUGUI>();
                    if (textComponent != null)
                    {
                        textComponent.text = $"{gestureName}: {similarity:F3}";
                        textComponent.color = isHighlighted ? Color.green : Color.white;
                    }
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[GestureRecognitionUIManager::UpdateSimilarityDisplay] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 启动相似度保持显示计时器（5秒）
        /// </summary>
        /// <param name="gestureName">手势名称</param>
        /// <param name="similarity">相似度</param>
        public void StartDisplayTimer(string gestureName, float similarity)
        {
            _displayTimers[gestureName] = 5f;
            _currentSimilarities[gestureName] = similarity;
            UpdateSimilarityDisplay(gestureName, similarity, true);
        }
        
        /// <summary>
        /// 更新当前相似度
        /// </summary>
        /// <param name="gestureName">手势名称</param>
        /// <param name="similarity">相似度</param>
        public void UpdateCurrentSimilarity(string gestureName, float similarity)
        {
            _currentSimilarities[gestureName] = similarity;
            
            // 检查是否在保持显示期间
            bool isInDisplayPeriod = _displayTimers.ContainsKey(gestureName);
            UpdateSimilarityDisplay(gestureName, similarity, isInDisplayPeriod);
        }
        
        /// <summary>
        /// 更新显示计时器（相似度保持显示5秒功能）
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        public void UpdateDisplayTimers(float deltaTime)
        {
            try
            {
                var expiredKeys = new List<string>();
                
                foreach (var kvp in _displayTimers.ToList())
                {
                    var newTime = kvp.Value - deltaTime;
                    if (newTime <= 0)
                    {
                        expiredKeys.Add(kvp.Key);
                    }
                    else
                    {
                        _displayTimers[kvp.Key] = newTime;
                        // 继续保持高亮显示
                        var currentSimilarity = _currentSimilarities.GetValueOrDefault(kvp.Key, 0f);
                        UpdateSimilarityDisplay(kvp.Key, currentSimilarity, true);
                    }
                }
                
                // 清理过期的计时器
                foreach (var key in expiredKeys)
                {
                    _displayTimers.Remove(key);
                    // 恢复正常显示
                    var currentSimilarity = _currentSimilarities.GetValueOrDefault(key, 0f);
                    UpdateSimilarityDisplay(key, currentSimilarity, false);
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[GestureRecognitionUIManager::UpdateDisplayTimers] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 清理所有计时器和状态
        /// </summary>
        public void ClearAll()
        {
            _displayTimers.Clear();
            _currentSimilarities.Clear();
        }
        
        /// <summary>
        /// 获取识别状态文本
        /// </summary>
        /// <param name="isActive">是否激活</param>
        /// <param name="loadedTemplates">已加载模板数量</param>
        /// <returns>状态文本</returns>
        public string GetRecognitionStatusText(bool isActive, int loadedTemplates)
        {
            var status = isActive ? "Active" : "Inactive";
            return $"Recognition: {status}, Templates: {loadedTemplates}";
        }

        /// <summary>
        /// 获取当前所有相似度数据
        /// </summary>
        /// <returns>相似度数据字典</returns>
        public Dictionary<string, float> GetCurrentSimilarities()
        {
            return new Dictionary<string, float>(_currentSimilarities);
        }

        /// <summary>
        /// 获取已加载的模板列表
        /// </summary>
        /// <returns>模板名称列表</returns>
        public List<string> GetLoadedTemplates()
        {
            return new List<string>(_templateListItems.Keys);
        }

        /// <summary>
        /// 检查模板是否在高亮显示期间
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <returns>是否高亮显示</returns>
        public bool IsTemplateHighlighted(string templateName)
        {
            return _displayTimers.ContainsKey(templateName);
        }
    }
}
