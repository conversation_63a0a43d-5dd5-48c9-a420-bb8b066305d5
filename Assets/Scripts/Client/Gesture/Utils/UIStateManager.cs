using System;
using System.Collections.Generic;
using QFramework;
using TMPro;
using UnityEngine.UI;

namespace GameClient.Gesture
{
    /// <summary>
    /// UI状态管理器，统一管理按钮显示和状态切换
    /// </summary>
    public class UIStateManager
    {
        /// <summary>
        /// UI状态枚举
        /// </summary>
        public enum UIState
        {
            None,
            Default, // 显示：开始录制、回放、识别
            Recording, // 隐藏所有按钮，等待S键录制
            Playback, // 显示：保存模版、停止按钮
            Recognition // 隐藏所有按钮，等待B键停止识别
        }

        private UIState _currentState = UIState.None;

        // 按钮引用
        private Button _startRecordingButton;
        private Button _playbackButton;
        private Button _recognitionButton;
        private Button _saveTemplateButton;

        // 按钮文本管理
        private readonly Dictionary<Button, string> _originalButtonTexts = new();

        /// <summary>
        /// 当前UI状态
        /// </summary>
        public UIState CurrentState => _currentState;

        /// <summary>
        /// 状态变化事件
        /// </summary>
        public event Action<UIState> OnStateChanged;

        /// <summary>
        /// 初始化UI状态管理器
        /// </summary>
        /// <param name="startRecording">开始录制按钮</param>
        /// <param name="playback">回放按钮</param>
        /// <param name="recognition">识别按钮</param>
        /// <param name="saveTemplate">保存模版按钮</param>
        public void Initialize(Button startRecording, Button playback, Button recognition, Button saveTemplate)
        {
            _startRecordingButton = startRecording;
            _playbackButton = playback;
            _recognitionButton = recognition;
            _saveTemplateButton = saveTemplate;

            // 保存原始按钮文本
            SaveOriginalButtonTexts();

            // 设置初始状态
            SetState(UIState.Default);
        }

        /// <summary>
        /// 设置UI状态
        /// </summary>
        /// <param name="newState">新状态</param>
        public void SetState(UIState newState)
        {
            try
            {
                if (_currentState == newState)
                {
                    LogKit.I($"[{GetType().FullName}::SetState] Already in state: {newState}");
                    return;
                }

                // 验证状态切换是否合法
                if (!CanSwitchTo(newState))
                {
                    LogKit.W($"[{GetType().FullName}::SetState] Invalid state transition: {_currentState} -> {newState}");
                    return;
                }

                var oldState = _currentState;
                _currentState = newState;

                // 更新按钮显示
                UpdateButtonVisibility();

                // 触发状态变化事件
                OnStateChanged?.Invoke(newState);

                LogKit.I($"[{GetType().FullName}::SetState] State changed: {oldState} -> {newState}");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::SetState] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新按钮显示状态
        /// </summary>
        private void UpdateButtonVisibility()
        {
            try
            {
                // 默认隐藏所有按钮
                SetButtonActive(_startRecordingButton, false);
                SetButtonActive(_playbackButton, false);
                SetButtonActive(_recognitionButton, false);
                SetButtonActive(_saveTemplateButton, false);

                // 根据状态显示对应按钮和设置文本
                switch (_currentState)
                {
                    case UIState.Default:
                        // 默认状态：显示开始录制、回放、识别按钮，恢复原始文本
                        SetButtonActive(_startRecordingButton, true);
                        SetButtonActive(_playbackButton, true);
                        SetButtonActive(_recognitionButton, true);
                        RestoreButtonText(_playbackButton);
                        RestoreButtonText(_recognitionButton);
                        LogKit.I($"[{GetType().FullName}::UpdateButtonVisibility] Default state: showing main buttons with original text");
                    break;

                    case UIState.Recording:
                        // 录制状态：隐藏所有按钮，等待S键录制
                        LogKit.I($"[{GetType().FullName}::UpdateButtonVisibility] Recording state: hiding all buttons");
                    break;

                    case UIState.Playback:
                        // 回放状态：显示保存模版按钮和回放按钮（改名为"停止"）
                        SetButtonActive(_saveTemplateButton, true);
                        SetButtonActive(_playbackButton, true);
                        SetButtonText(_playbackButton, "停止");
                        LogKit.I($"[{GetType().FullName}::UpdateButtonVisibility] Playback state: showing save template and stop button");
                    break;

                    case UIState.Recognition:
                        // 识别状态：显示识别按钮（改名为"停止"）
                        SetButtonActive(_recognitionButton, true);
                        SetButtonText(_recognitionButton, "停止");
                        LogKit.I($"[{GetType().FullName}::UpdateButtonVisibility] Recognition state: showing stop button");
                    break;
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::UpdateButtonVisibility] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全设置按钮激活状态
        /// </summary>
        /// <param name="button">按钮</param>
        /// <param name="active">是否激活</param>
        private void SetButtonActive(Button button, bool active)
        {
            if (button != null && button.gameObject != null)
            {
                button.gameObject.SetActive(active);
            }
        }

        /// <summary>
        /// 检查是否可以切换到指定状态
        /// </summary>
        /// <param name="targetState">目标状态</param>
        /// <returns>是否可以切换</returns>
        private bool CanSwitchTo(UIState targetState)
        {
            return _currentState switch
                   {
                       UIState.Default => targetState is UIState.Recording or UIState.Playback or UIState.Recognition,
                       UIState.Recording or UIState.Playback or UIState.Recognition => targetState == UIState.Default,
                       UIState.None => true,
                       _ => false
                   };
        }

        /// <summary>
        /// 强制返回默认状态
        /// </summary>
        public void ReturnToDefault()
        {
            SetState(UIState.Default);
        }

        /// <summary>
        /// 保存所有按钮的原始文本
        /// </summary>
        private void SaveOriginalButtonTexts()
        {
            try
            {
                SaveButtonText(_playbackButton);
                SaveButtonText(_recognitionButton);

                LogKit.I($"[{GetType().FullName}::SaveOriginalButtonTexts] Saved original button texts");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::SaveOriginalButtonTexts] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存单个按钮的原始文本
        /// </summary>
        /// <param name="button">按钮</param>
        private void SaveButtonText(Button button)
        {
            if (button != null)
            {
                var textComponent = button.GetComponentInChildren<TextMeshProUGUI>();
                if (textComponent != null)
                {
                    _originalButtonTexts[button] = textComponent.text;
                    LogKit.I($"[{GetType().FullName}::SaveButtonText] Saved text for button: {textComponent.text}");
                }
            }
        }

        /// <summary>
        /// 设置按钮文本
        /// </summary>
        /// <param name="button">按钮</param>
        /// <param name="newText">新文本</param>
        private void SetButtonText(Button button, string newText)
        {
            if (button != null)
            {
                var textComponent = button.GetComponentInChildren<TextMeshProUGUI>();
                if (textComponent != null)
                {
                    textComponent.text = newText;
                    LogKit.I($"[{GetType().FullName}::SetButtonText] Set button text to: {newText}");
                }
            }
        }

        /// <summary>
        /// 恢复按钮原始文本
        /// </summary>
        /// <param name="button">按钮</param>
        private void RestoreButtonText(Button button)
        {
            if (button != null && _originalButtonTexts.ContainsKey(button))
            {
                SetButtonText(button, _originalButtonTexts[button]);
                LogKit.I($"[{GetType().FullName}::RestoreButtonText] Restored button text to: {_originalButtonTexts[button]}");
            }
        }

        /// <summary>
        /// 获取状态描述
        /// </summary>
        /// <param name="state">状态</param>
        /// <returns>描述文本</returns>
        public static string GetStateDescription(UIState state)
        {
            return state switch
                   {
                       UIState.Default => "待机状态 - 选择操作",
                       UIState.Recording => "录制中 - 按住S键录制",
                       UIState.Playback => "回放中 - 可保存模版或停止",
                       UIState.Recognition => "识别中 - 点击停止按钮结束",
                       _ => "未知状态"
                   };
        }
    }
}
