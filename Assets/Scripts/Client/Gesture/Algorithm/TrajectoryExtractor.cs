using System;
using System.Collections.Generic;
using System.Linq;
using QFramework;
using UnityEngine;

namespace GameClient.Gesture
{
    /// <summary>
    /// 轨迹提取器，专门用于从MovementData序列中提取关节点轨迹曲线
    /// 这是原始需求的核心：通过轨迹形成一条曲线然后计算曲线的相似度
    /// </summary>
    public class TrajectoryExtractor
    {
        /// <summary>
        /// 关节点索引枚举（对应MovementData.Datas数组）
        /// </summary>
        public enum JointIndex
        {
            Head = 0,       // 头部
            LeftHand = 1,   // 左手
            RightHand = 2,  // 右手
            LeftFoot = 3,   // 左脚
            RightFoot = 4   // 右脚
        }
        
        /// <summary>
        /// 轨迹曲线数据结构
        /// </summary>
        public class TrajectoryCurve
        {
            public List<Vector3> Points { get; set; } = new();
            public List<float> Timestamps { get; set; } = new();
            public JointIndex JointType { get; set; }
            public bool IsRelativeToHead { get; set; }
            
            /// <summary>
            /// 获取曲线长度
            /// </summary>
            public float GetCurveLength()
            {
                float length = 0f;
                for (int i = 1; i < Points.Count; i++)
                {
                    length += Vector3.Distance(Points[i-1], Points[i]);
                }
                return length;
            }
            
            /// <summary>
            /// 获取曲线的平均速度
            /// </summary>
            public float GetAverageVelocity()
            {
                if (Timestamps.Count < 2) return 0f;
                var totalTime = Timestamps.Last() - Timestamps.First();
                return totalTime > 0 ? GetCurveLength() / totalTime : 0f;
            }
        }
        
        /// <summary>
        /// 提取单个关节点的轨迹曲线（原始需求的核心功能）
        /// </summary>
        /// <param name="sequence">MovementData序列</param>
        /// <param name="jointIndex">要提取的关节点</param>
        /// <param name="useHeadOrientation">是否以头部朝向为参考系</param>
        /// <returns>轨迹曲线</returns>
        public TrajectoryCurve ExtractJointTrajectory(List<TimestampedMovementData> sequence, JointIndex jointIndex, bool useHeadOrientation = true)
        {
            try
            {
                var curve = new TrajectoryCurve
                {
                    JointType = jointIndex,
                    IsRelativeToHead = useHeadOrientation
                };
                
                if (sequence == null || sequence.Count == 0)
                {
                    return curve;
                }
                
                var startTime = sequence.First().TimestampMs / 1000f;
                
                foreach (var data in sequence)
                {
                    if (data.MovementData?.Datas == null || data.MovementData.Datas.Length <= (int)jointIndex)
                    {
                        continue;
                    }
                    
                    var timestamp = data.TimestampMs / 1000f - startTime;
                    Vector3 jointPosition;
                    
                    if (useHeadOrientation && jointIndex != JointIndex.Head)
                    {
                        // 以头部朝向为参考系计算相对位置（原始需求的测试案例）
                        jointPosition = CalculateRelativeToHead(data.MovementData, jointIndex);
                    }
                    else
                    {
                        // 直接使用世界坐标（兼容不设置头部朝向的情况）
                        var joint = data.MovementData.Datas[(int)jointIndex];
                        jointPosition = new Vector3(joint.Pos.x, joint.Pos.y, joint.Pos.z);
                    }
                    
                    curve.Points.Add(jointPosition);
                    curve.Timestamps.Add(timestamp);
                }
                
                LogKit.I($"[{GetType().FullName}::ExtractJointTrajectory] Extracted {curve.Points.Count} points for {jointIndex}");
                return curve;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::ExtractJointTrajectory] Error: {ex.Message}");
                return new TrajectoryCurve { JointType = jointIndex, IsRelativeToHead = useHeadOrientation };
            }
        }
        
        /// <summary>
        /// 计算相对于头部朝向的位置（原始需求：以头部朝向为方向计算）
        /// </summary>
        private Vector3 CalculateRelativeToHead(TFGShare.Protocol.MovementData movementData, JointIndex jointIndex)
        {
            if (movementData.Datas.Length <= (int)JointIndex.Head || 
                movementData.Datas.Length <= (int)jointIndex)
            {
                return Vector3.zero;
            }
            
            var headData = movementData.Datas[(int)JointIndex.Head];
            var jointData = movementData.Datas[(int)jointIndex];
            
            // 头部位置和旋转
            var headPosition = new Vector3(headData.Pos.x, headData.Pos.y, headData.Pos.z);
            var headRotation = new Quaternion(headData.Rot.x, headData.Rot.y, headData.Rot.z, headData.Rot.w);
            
            // 关节点位置
            var jointPosition = new Vector3(jointData.Pos.x, jointData.Pos.y, jointData.Pos.z);
            
            // 计算相对位置
            var relativePosition = jointPosition - headPosition;
            
            // 转换到头部朝向的局部坐标系
            var localPosition = Quaternion.Inverse(headRotation) * relativePosition;
            
            return localPosition;
        }
        
        /// <summary>
        /// 提取多个关节点的轨迹曲线（支持1~多个节点，无论是否启用头部朝向）
        /// </summary>
        /// <param name="sequence">MovementData序列</param>
        /// <param name="selectedJoints">选择的关节点列表</param>
        /// <param name="useHeadOrientation">是否以头部朝向为参考系</param>
        /// <returns>轨迹曲线字典</returns>
        public Dictionary<JointIndex, TrajectoryCurve> ExtractMultipleJointTrajectories(
            List<TimestampedMovementData> sequence,
            List<JointIndex> selectedJoints,
            bool useHeadOrientation = true)
        {
            var trajectories = new Dictionary<JointIndex, TrajectoryCurve>();

            if (selectedJoints == null || selectedJoints.Count == 0)
            {
                return trajectories;
            }

            foreach (var joint in selectedJoints)
            {
                trajectories[joint] = ExtractJointTrajectory(sequence, joint, useHeadOrientation);
            }

            return trajectories;
        }
        
        /// <summary>
        /// 应用滤波处理（原始需求：考虑滤波功能，比如防止尖刺）
        /// </summary>
        /// <param name="curve">原始轨迹曲线</param>
        /// <param name="filterType">滤波类型</param>
        /// <returns>滤波后的轨迹曲线</returns>
        public TrajectoryCurve ApplyFilter(TrajectoryCurve curve, FilterType filterType = FilterType.MovingAverage)
        {
            if (curve.Points.Count < 3)
            {
                return curve;
            }
            
            var filteredCurve = new TrajectoryCurve
            {
                JointType = curve.JointType,
                IsRelativeToHead = curve.IsRelativeToHead,
                Timestamps = new List<float>(curve.Timestamps)
            };
            
            switch (filterType)
            {
                case FilterType.MovingAverage:
                    filteredCurve.Points = ApplyMovingAverageFilter(curve.Points);
                    break;
                case FilterType.MedianFilter:
                    filteredCurve.Points = ApplyMedianFilter(curve.Points);
                    break;
                case FilterType.SpikeRemoval:
                    filteredCurve.Points = ApplySpikeRemovalFilter(curve.Points);
                    break;
            }
            
            return filteredCurve;
        }
        
        /// <summary>
        /// 滤波类型枚举
        /// </summary>
        public enum FilterType
        {
            MovingAverage,  // 移动平均
            MedianFilter,   // 中值滤波
            SpikeRemoval    // 尖刺去除
        }
        
        /// <summary>
        /// 移动平均滤波
        /// </summary>
        private List<Vector3> ApplyMovingAverageFilter(List<Vector3> points, int windowSize = 3)
        {
            var filtered = new List<Vector3>();
            
            for (int i = 0; i < points.Count; i++)
            {
                var sum = Vector3.zero;
                var count = 0;
                
                for (int j = Math.Max(0, i - windowSize/2); j <= Math.Min(points.Count - 1, i + windowSize/2); j++)
                {
                    sum += points[j];
                    count++;
                }
                
                filtered.Add(sum / count);
            }
            
            return filtered;
        }
        
        /// <summary>
        /// 中值滤波
        /// </summary>
        private List<Vector3> ApplyMedianFilter(List<Vector3> points, int windowSize = 3)
        {
            var filtered = new List<Vector3>();
            
            for (int i = 0; i < points.Count; i++)
            {
                var window = new List<Vector3>();
                
                for (int j = Math.Max(0, i - windowSize/2); j <= Math.Min(points.Count - 1, i + windowSize/2); j++)
                {
                    window.Add(points[j]);
                }
                
                // 简化的中值计算（取中间值）
                window.Sort((a, b) => a.magnitude.CompareTo(b.magnitude));
                filtered.Add(window[window.Count / 2]);
            }
            
            return filtered;
        }
        
        /// <summary>
        /// 尖刺去除滤波（原始需求：防止尖刺）
        /// </summary>
        private List<Vector3> ApplySpikeRemovalFilter(List<Vector3> points, float threshold = 2.0f)
        {
            if (points.Count < 3) return new List<Vector3>(points);
            
            var filtered = new List<Vector3> { points[0] };
            
            for (int i = 1; i < points.Count - 1; i++)
            {
                var prev = points[i - 1];
                var curr = points[i];
                var next = points[i + 1];
                
                // 计算当前点与前后点的距离
                var distToPrev = Vector3.Distance(curr, prev);
                var distToNext = Vector3.Distance(curr, next);
                var avgDist = (distToPrev + distToNext) / 2f;
                
                // 如果距离过大，认为是尖刺，用前后点的平均值替代
                if (avgDist > threshold)
                {
                    filtered.Add((prev + next) / 2f);
                }
                else
                {
                    filtered.Add(curr);
                }
            }
            
            filtered.Add(points.Last());
            return filtered;
        }
    }
}
