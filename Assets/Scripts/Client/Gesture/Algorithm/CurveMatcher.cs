using System;
using System.Collections.Generic;
using QFramework;
using TFGShare.Utility;
using UnityEngine;

namespace GameClient.Gesture
{
    /// <summary>
    /// 极简的Curve匹配器，专注于连续识别和Curve匹配
    /// 满足原始需求：连续识别、只需要Curve匹配、滤波防尖刺
    /// </summary>
    public static class CurveMatcher
    {
        // 性能优化：DTW矩阵复用
        private static float[,] _dtwMatrix;
        private static int _lastMatrixSize = 0;

        /// <summary>
        /// 在实时输入中连续匹配Curve
        /// </summary>
        /// <param name="templateCurve">模板曲线</param>
        /// <param name="inputCurve">输入曲线</param>
        /// <param name="threshold">匹配阈值</param>
        /// <param name="enableDetailedLogging">启用详细日志</param>
        /// <returns>最佳相似度</returns>
        public static float MatchCurve(List<Vector3> templateCurve, List<Vector3> inputCurve, float threshold = 0.8f, bool enableDetailedLogging = false)
        {
            try
            {
                if (templateCurve == null || inputCurve == null ||
                    templateCurve.Count == 0 || inputCurve.Count == 0)
                {
                    return 0f;
                }

                // 如果输入比模板短，直接计算相似度
                if (inputCurve.Count <= templateCurve.Count)
                {
                    return CalculateDTWSimilarity(templateCurve, inputCurve);
                }

                // 连续识别：滑动窗口匹配
                float bestSimilarity = 0f;
                int templateLength = templateCurve.Count;
                int bestWindowIndex = -1;
                int totalWindows = inputCurve.Count - templateLength + 1;

                if (enableDetailedLogging)
                {
                    LogKit.I($"[CurveMatcher::MatchCurve] Starting sliding window matching. Template length: {templateLength}, Input length: {inputCurve.Count}, Total windows: {totalWindows}");
                }

                for (int i = 0; i <= inputCurve.Count - templateLength; i++)
                {
                    var windowCurve = inputCurve.GetRange(i, templateLength);
                    var similarity = CalculateDTWSimilarity(templateCurve, windowCurve);

                    if (enableDetailedLogging)
                    {
                        LogKit.I($"[CurveMatcher::MatchCurve] Window {i}/{totalWindows-1}: similarity = {similarity:F3}");
                    }

                    if (similarity > bestSimilarity)
                    {
                        bestSimilarity = similarity;
                        bestWindowIndex = i;
                    }

                    // 达到阈值立即返回（实时性）
                    if (similarity >= threshold)
                    {
                        if (enableDetailedLogging)
                        {
                            LogKit.I($"[CurveMatcher::MatchCurve] Threshold reached at window {i}. Similarity: {similarity:F3} >= {threshold:F3}");
                        }
                        return similarity;
                    }
                }

                if (enableDetailedLogging)
                {
                    LogKit.I($"[CurveMatcher::MatchCurve] Best match at window {bestWindowIndex} with similarity {bestSimilarity:F3}");
                }

                return bestSimilarity;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{typeof(CurveMatcher).FullName}::MatchCurve] Error: {ex.Message}");
                return 0f;
            }
        }

        /// <summary>
        /// DTW相似度计算（性能优化版：矩阵复用）
        /// </summary>
        private static float CalculateDTWSimilarity(List<Vector3> template, List<Vector3> input)
        {
            int m = template.Count;
            int n = input.Count;
            int requiredSize = (m + 1) * (n + 1);

            // 性能优化：复用DTW矩阵，避免频繁分配
            if (_dtwMatrix == null || _lastMatrixSize < requiredSize)
            {
                _dtwMatrix = new float[m + 1, n + 1];
                _lastMatrixSize = requiredSize;
            }

            var dtw = _dtwMatrix;

            // 初始化
            for (int i = 0; i <= m; i++) dtw[i, 0] = float.MaxValue;
            for (int j = 0; j <= n; j++) dtw[0, j] = float.MaxValue;
            dtw[0, 0] = 0;

            // 填充DTW矩阵
            for (int i = 1; i <= m; i++)
            {
                for (int j = 1; j <= n; j++)
                {
                    var cost = Vector3.Distance(template[i - 1], input[j - 1]);
                    dtw[i, j] = cost + Mathf.Min(
                                                 Mathf.Min(dtw[i - 1, j], dtw[i, j - 1]),
                                                 dtw[i - 1, j - 1]);
                }
            }

            // 转换为相似度
            var normalizedDistance = dtw[m, n] / Mathf.Max(m, n);
            return 1f / (1f + normalizedDistance);
        }

        /// <summary>
        /// 多部位Curve的综合匹配
        /// </summary>
        /// <param name="templateCurves">模板曲线字典（部位->曲线）</param>
        /// <param name="inputCurves">输入曲线字典（部位->曲线）</param>
        /// <param name="threshold">匹配阈值</param>
        /// <param name="enableDetailedLogging">启用详细日志</param>
        /// <returns>平均相似度</returns>
        public static float MatchMultipleCurves(Dictionary<string, List<Vector3>> templateCurves, Dictionary<string, List<Vector3>> inputCurves,
            float threshold = 0.8f, bool enableDetailedLogging = false)
        {
            try
            {
                if (templateCurves == null || inputCurves == null || templateCurves.Count == 0)
                {
                    return 0f;
                }

                float totalSimilarity = 0f;
                int validParts = 0;

                foreach (var kvp in templateCurves)
                {
                    var partName = kvp.Key;
                    var templateCurve = kvp.Value;

                    if (inputCurves.TryGetValue(partName, out var inputCurve))
                    {
                        var similarity = MatchCurve(templateCurve, inputCurve, threshold, enableDetailedLogging);
                        totalSimilarity += similarity;
                        validParts++;

                        if (enableDetailedLogging)
                        {
                            LogKit.I($"[CurveMatcher::MatchMultipleCurves] Part: {partName}, Similarity: {similarity:F3}");
                        }
                    }
                    else if (enableDetailedLogging)
                    {
                        LogKit.I($"[CurveMatcher::MatchMultipleCurves] Part: {partName} not found in input curves");
                    }
                }

                return validParts > 0 ? totalSimilarity / validParts : 0f;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{typeof(CurveMatcher).FullName}::MatchMultipleCurves] Error: {ex.Message}");
                return 0f;
            }
        }

        /// <summary>
        /// 滤波处理（防止尖刺）
        /// </summary>
        /// <param name="curve">原始曲线</param>
        /// <param name="windowSize">滤波窗口大小</param>
        /// <returns>滤波后的曲线</returns>
        public static List<Vector3> FilterCurve(List<Vector3> curve, int windowSize = 3)
        {
            if (curve == null || curve.Count < windowSize)
            {
                return new List<Vector3>(curve ?? new List<Vector3>());
            }

            var filtered = new List<Vector3>();

            for (int i = 0; i < curve.Count; i++)
            {
                var sum = Vector3.zero;
                var count = 0;

                // 移动平均滤波
                for (int j = Math.Max(0, i - windowSize / 2); j <= Math.Min(curve.Count - 1, i + windowSize / 2); j++)
                {
                    sum += curve[j];
                    count++;
                }

                filtered.Add(sum / count);
            }

            return filtered;
        }

        /// <summary>
        /// 尖刺去除滤波
        /// </summary>
        /// <param name="curve">原始曲线</param>
        /// <param name="threshold">尖刺阈值</param>
        /// <returns>去除尖刺后的曲线</returns>
        public static List<Vector3> RemoveSpikes(List<Vector3> curve, float threshold = 2.0f)
        {
            if (curve == null || curve.Count < 3)
            {
                return new List<Vector3>(curve ?? new List<Vector3>());
            }

            var filtered = new List<Vector3> { curve[0] };

            for (int i = 1; i < curve.Count - 1; i++)
            {
                var prev = curve[i - 1];
                var curr = curve[i];
                var next = curve[i + 1];

                var distToPrev = Vector3.Distance(curr, prev);
                var distToNext = Vector3.Distance(curr, next);
                var avgDist = (distToPrev + distToNext) / 2f;

                // 如果距离过大，认为是尖刺，用前后点的平均值替代
                if (avgDist > threshold)
                {
                    filtered.Add((prev + next) / 2f);
                }
                else
                {
                    filtered.Add(curr);
                }
            }

            filtered.Add(curve[curve.Count - 1]);
            return filtered;
        }
    }
}
