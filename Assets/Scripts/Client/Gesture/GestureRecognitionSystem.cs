using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;

using GameShare.Events;
using QFramework;
using TFGClient.Gesture;
using TFGShare.Utility;
using UnityEngine;

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势识别系统，负责加载模板、实时识别和相似度计算
    /// </summary>
    public class GestureRecognitionSystem : AbstractSystem
    {
        // 系统组件
        private GestureSystemConfig _config;

        // 极简：只保留dat文件数据
        private Dictionary<string, DatFileManager.DatFileData> _loadedDatFiles = new();
        
        // 模板管理
        private List<string> _enabledTemplates = new();
        
        // 实时识别
        private bool _isRecognitionActive = false;
        private List<TimestampedMovementData> _dataBuffer = new();
        private float _lastRecognitionTime = 0f;
        
        // 相似度保持显示功能（5秒）
        private Dictionary<string, DateTime> _displayEndTimes = new();
        private Dictionary<string, float> _currentSimilarities = new();
        
        // 性能优化（现在使用配置参数）

        // 性能监控
        private Stopwatch _recognitionStopwatch = new Stopwatch();
        private Dictionary<string, List<long>> _templateTimings = new Dictionary<string, List<long>>();
        private Dictionary<string, List<float>> _templateSimilarities = new Dictionary<string, List<float>>();
        private int _totalRecognitionCount = 0;
        private int _successfulRecognitionCount = 0;
        private DateTime _lastBufferCleanTime = DateTime.UtcNow;

        // 多线程处理器
        private ThreadedRecognitionProcessor _threadedProcessor;
        private IUnRegister _updateRegister;
        
        protected override void OnInit()
        {
            try
            {
                // 加载配置
                _config = Resources.Load<GestureSystemConfig>("GestureSystemConfig");
                if (_config == null)
                {
                    LogKit.E($"[{GetType().FullName}::OnInit] Failed to load GestureSystemConfig");
                    return;
                }
                
                // 初始化多线程处理器
                _threadedProcessor = new ThreadedRecognitionProcessor(_config, Environment.ProcessorCount / 2);

                // 注册事件监听
                this.RegisterEvent<BattlePlayerMovementEvent>(OnBattlePlayerMovement);

                LogKit.I($"[{GetType().FullName}::OnInit] GestureRecognitionSystem initialized with multithreading");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnInit] Error: {ex.Message}");
            }
        }
        
        protected override void OnDeinit()
        {
            try
            {
                // 停止识别
                StopRecognition();

                // 清理多线程处理器
                _threadedProcessor?.Dispose();

                // 注销事件监听
                this.UnRegisterEvent<BattlePlayerMovementEvent>(OnBattlePlayerMovement);

                LogKit.I($"[{GetType().FullName}::OnDeinit] GestureRecognitionSystem deinitialized");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnDeinit] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 加载手势模板
        /// </summary>
        /// <param name="templatesPath">模板目录路径</param>
        public void LoadGestureTemplates(string templatesPath = "GestureTemplates")
        {
            try
            {
                var directory = Path.Combine(Application.dataPath, templatesPath);
                if (!Directory.Exists(directory))
                {
                    LogKit.W($"[{GetType().FullName}::LoadGestureTemplates] Templates directory not found: {directory}");
                    return;
                }
                
                _loadedDatFiles.Clear();
                _enabledTemplates.Clear();

                // 加载所有dat文件
                _loadedDatFiles = DatFileManager.LoadAllDatFiles();

                foreach (var kvp in _loadedDatFiles)
                {
                    _enabledTemplates.Add(kvp.Key);
                }

                LogKit.I($"[{GetType().FullName}::LoadGestureTemplates] Loaded {_loadedDatFiles.Count} dat files");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::LoadGestureTemplates] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 开始实时识别
        /// </summary>
        public void StartRecognition()
        {
            try
            {
                if (_isRecognitionActive)
                {
                    LogKit.W($"[{GetType().FullName}::StartRecognition] Recognition already active");
                    return;
                }
                
                if (_loadedDatFiles.Count == 0)
                {
                    LogKit.W($"[{GetType().FullName}::StartRecognition] No dat files loaded");
                    return;
                }
                
                _isRecognitionActive = true;
                _dataBuffer.Clear();
                _displayEndTimes.Clear();
                _currentSimilarities.Clear();
                _lastRecognitionTime = Time.time;

                // 启动多线程处理器
                _threadedProcessor?.Start();

                // 注册主线程更新回调
                _updateRegister = ActionKit.OnUpdate.Register(ProcessRecognitionResults);

                // 发送识别开始事件
                this.SendEvent(new GestureRecognitionStartedEvent
                {
                    StartTime = DateTime.UtcNow,
                    LoadedTemplates = new List<string>(_enabledTemplates)
                });

                LogKit.I($"[{GetType().FullName}::StartRecognition] Recognition started with {_enabledTemplates.Count} templates and multithreading");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::StartRecognition] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 停止实时识别
        /// </summary>
        public void StopRecognition()
        {
            try
            {
                if (!_isRecognitionActive)
                {
                    return;
                }
                
                _isRecognitionActive = false;
                _dataBuffer.Clear();
                _displayEndTimes.Clear();
                _currentSimilarities.Clear();

                // 停止多线程处理器
                _threadedProcessor?.Stop();

                // 解注册主线程更新回调
                _updateRegister?.UnRegister();
                _updateRegister = null;

                // 发送识别停止事件
                this.SendEvent(new GestureRecognitionStoppedEvent
                {
                    StopTime = DateTime.UtcNow,
                    TotalRecognitions = _totalRecognitionCount
                });

                LogKit.I($"[{GetType().FullName}::StopRecognition] Recognition stopped with multithreading");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::StopRecognition] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 处理 BattlePlayerMovementEvent 事件
        /// </summary>
        private void OnBattlePlayerMovement(BattlePlayerMovementEvent e)
        {
            try
            {
                if (!_isRecognitionActive || e?.MovementData == null)
                {
                    return;
                }
                
                // 添加到缓冲区
                var timestampedData = new TimestampedMovementData
                {
                    TimestampMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    MovementData = e.MovementData.DeepClone()
                };
                
                _dataBuffer.Add(timestampedData);
                
                // 维护缓冲区大小
                if (_dataBuffer.Count > _config.maxBufferSize)
                {
                    _dataBuffer.RemoveAt(0);
                    _lastBufferCleanTime = DateTime.UtcNow;
                }
                
                // 检查识别间隔
                var currentTime = Time.time;
                if (currentTime - _lastRecognitionTime >= _config.recognitionInterval)
                {
                    PerformRecognition();
                    _lastRecognitionTime = currentTime;
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnBattlePlayerMovement] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 执行实时识别（使用多线程处理器）
        /// </summary>
        private void PerformRecognition()
        {
            try
            {
                if (_dataBuffer.Count < _config.minDataPoints) // 需要最少的数据点
                {
                    if (_config.enableBufferMonitoring)
                    {
                        LogKit.I($"[{GetType().FullName}::PerformRecognition] Buffer too small: {_dataBuffer.Count}/{_config.minDataPoints}");
                    }
                    return;
                }

                var currentTime = DateTime.UtcNow;
                _totalRecognitionCount++;

                // 创建输入序列的副本（避免多线程访问冲突）
                var inputSequenceCopy = new List<TimestampedMovementData>(_dataBuffer);
                _totalRecognitionCount++;

                // 开始整体性能计时
                if (_config.enableTimingStats)
                {
                    _recognitionStopwatch.Restart();
                }

                // 监控缓冲区状态
                if (_config.enableBufferMonitoring)
                {
                    var timeSinceLastClean = (currentTime - _lastBufferCleanTime).TotalSeconds;
                    LogKit.I($"[{GetType().FullName}::PerformRecognition] Buffer size: {_dataBuffer.Count}/{_config.maxBufferSize}, Time since last clean: {timeSinceLastClean:F2}s");
                }

                // 提交多线程识别任务
                foreach (var datFileName in _enabledTemplates)
                {
                    if (!_loadedDatFiles.TryGetValue(datFileName, out var datData))
                    {
                        continue;
                    }

                    var task = new RecognitionTask
                    {
                        TaskId = Guid.NewGuid().ToString(),
                        GestureName = datFileName,
                        DatData = datData,
                        InputSequence = inputSequenceCopy,
                        SubmitTime = currentTime
                    };

                    _threadedProcessor?.SubmitTask(task);
                }

                // 性能监控
                if (_config.enablePerformanceMonitoring && _totalRecognitionCount % 10 == 0)
                {
                    var stats = _threadedProcessor?.GetPerformanceStats();
                    if (stats != null)
                    {
                        LogKit.I($"[{GetType().FullName}::PerformRecognition] Submitted {_enabledTemplates.Count} tasks. " +
                                $"Processor stats: {stats.TotalTasksProcessed} processed, {stats.AverageProcessingTime:F2}ms avg, " +
                                $"Queue: {stats.QueueSize}, Results: {stats.ResultQueueSize}");
                    }
                }

                // 内存监控
                if (_config.enableMemoryMonitoring && _totalRecognitionCount % _config.memoryMonitoringInterval == 0)
                {
                    var memoryUsage = GC.GetTotalMemory(false) / 1024 / 1024; // MB
                    LogKit.I($"[{GetType().FullName}::PerformRecognition] Memory usage: {memoryUsage}MB, Total recognitions: {_totalRecognitionCount}");
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::PerformRecognition] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理多线程识别结果
        /// 在主线程中调用，用于发送事件
        /// </summary>
        private void ProcessRecognitionResults()
        {
            if (!_isRecognitionActive || _threadedProcessor == null) return;

            try
            {
                var results = _threadedProcessor.GetResults();

                foreach (var result in results)
                {
                    // 记录性能数据
                    if (_config.enableTimingStats)
                    {
                        if (!_templateTimings.ContainsKey(result.GestureName))
                        {
                            _templateTimings[result.GestureName] = new List<long>();
                        }
                        _templateTimings[result.GestureName].Add(result.ProcessingTime);

                        // 保持最近N次记录
                        if (_templateTimings[result.GestureName].Count > _config.performanceStatsCount)
                        {
                            _templateTimings[result.GestureName].RemoveAt(0);
                        }
                    }

                    // 记录相似度数据
                    if (_config.enableSimilarityLogging)
                    {
                        if (!_templateSimilarities.ContainsKey(result.GestureName))
                        {
                            _templateSimilarities[result.GestureName] = new List<float>();
                        }
                        _templateSimilarities[result.GestureName].Add(result.Similarity);

                        // 保持最近N次记录
                        if (_templateSimilarities[result.GestureName].Count > _config.performanceStatsCount)
                        {
                            _templateSimilarities[result.GestureName].RemoveAt(0);
                        }

                        LogKit.I($"[{GetType().FullName}::ProcessRecognitionResults] Template: {result.GestureName}, Similarity: {result.Similarity:F3}, Time: {result.ProcessingTime}ms, Worker: {result.WorkerId}");
                    }

                    // 更新当前相似度
                    _currentSimilarities[result.GestureName] = result.Similarity;

                    // 发送实时更新事件
                    this.SendEvent(new GestureRecognitionUpdateEvent
                    {
                        GestureName = result.GestureName,
                        Similarity = result.Similarity,
                        UpdateTime = result.Timestamp
                    });

                    // 检查是否达到识别阈值
                    if (result.Success && result.Similarity >= _config.recognitionThreshold)
                    {
                        // 创建DatFileData用于OnGestureRecognized
                        if (_loadedDatFiles.TryGetValue(result.GestureName, out var datData))
                        {
                            OnGestureRecognized(datData, result.Similarity);
                            _successfulRecognitionCount++;
                        }
                    }

                    // 处理错误
                    if (!string.IsNullOrEmpty(result.ErrorMessage))
                    {
                        LogKit.E($"[{GetType().FullName}::ProcessRecognitionResults] Recognition error for {result.GestureName}: {result.ErrorMessage}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::ProcessRecognitionResults] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理手势识别成功
        /// </summary>
        private void OnGestureRecognized(DatFileManager.DatFileData datData, float similarity)
        {
            try
            {
                // 启动相似度保持显示计时器（5秒）
                _displayEndTimes[datData.name] = DateTime.UtcNow.AddSeconds(5);

                // 发送识别成功事件
                this.SendEvent(new GestureRecognizedEvent
                {
                    GestureName = datData.name,
                    Similarity = similarity,
                    RecognitionTime = DateTime.UtcNow
                });

                LogKit.I($"[{GetType().FullName}::OnGestureRecognized] Gesture recognized: {datData.name} (similarity: {similarity:F3})");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnGestureRecognized] Error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取识别状态
        /// </summary>
        public bool IsRecognitionActive => _isRecognitionActive;
        
        /// <summary>
        /// 获取已加载的dat文件数量
        /// </summary>
        public int LoadedTemplateCount => _loadedDatFiles.Count;
        
        /// <summary>
        /// 获取当前缓冲区大小
        /// </summary>
        public int BufferSize => _dataBuffer.Count;
    }
}
