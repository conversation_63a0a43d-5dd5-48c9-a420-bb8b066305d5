using System;
using System.Collections.Generic;
using QFramework;
using QFramework.PrefsKit;

namespace Client.Model
{
    //账号类型
    public enum AccountType
    {
        None,
        PhoneNumber,
        Email
    }

    //验证类型
    public enum CheckType
    {
        None,
        Verification,
        Password
    }

    public class ViewAccountCheckTypeChangedEvent
    {
        public readonly CheckType CheckType;

        public ViewAccountCheckTypeChangedEvent(CheckType checkType)
        {
            CheckType = checkType;
        }
    }

    public class ViewAccountTypeChangedEvent
    {
        public readonly AccountType AccountType;

        public ViewAccountTypeChangedEvent(AccountType accountType)
        {
            AccountType = accountType;
        }
    }

    public class ViewLoginMethodChangedEvent
    {
        public readonly LoginMethod LoginMethod;

        public ViewLoginMethodChangedEvent(LoginMethod loginMethod)
        {
            LoginMethod = loginMethod;
        }
    }

    public enum LoginMethod
    {
        None,
        EmailWithPassword,
        PhoneWithVerificationCode,
        PhoneWithPassword
    }

    //账户首选项模型
    //默认登录模式
    //本地记录的账号
    //是否自动登录
    //保存与清除本地账号留存
    public class ClientAccountPreferencesModel : AbstractModel
    {
        public LoginMethod LoginMethod
        {
            get
            {
                switch (this.AccountType)
                {
                    case AccountType.Email:
                        // 邮箱登录总是使用密码
                        return LoginMethod.EmailWithPassword;
                    case AccountType.PhoneNumber:
                        // 根据手机号的验证类型返回对应方式
                        return this.CheckType == CheckType.Verification
                            ? LoginMethod.PhoneWithVerificationCode
                            : LoginMethod.PhoneWithPassword;
                    default:
                        return LoginMethod.PhoneWithVerificationCode;
                }
            }
            set => SetLoginMethodInternal(value, true);
        }

        //从本地存储中获取AccountType预设，没有默认邮箱登录
        public AccountType AccountType
        {
            get
            {
                var typeInt = PrefsKitUtil.PrefsUtil.Get<int>("AccountType", 1); //1默认手机登录
                return (AccountType)typeInt;
            }
            set
            {
                var typeInt = (int)value;
                this.SendEvent(new ViewAccountTypeChangedEvent(value));
                PrefsKitUtil.PrefsUtil.Set("AccountType", typeInt); //保存登录方式预设
            }
        }

        //从本地存储中获取CheckType预设，没有默认密码登录
        public CheckType CheckType
        {
            get
            {
                var typeInt = PrefsKitUtil.PrefsUtil.Get<int>("CheckType", 1); //2默认验证码登录
                return (CheckType)typeInt;
            }
            set
            {
                var typeInt = (int)value;
                this.SendEvent(new ViewAccountCheckTypeChangedEvent(value));
                PrefsKitUtil.PrefsUtil.Set("CheckType", typeInt); //保存登录方式预设
            }
        }

        //从本地存储中获取是否自动登录预设，默认不自动登录
        public bool IsAutoLogin
        {
            get => PrefsKitUtil.PrefsUtil.Get("IsAutoLogin", false);
            set => PrefsKitUtil.PrefsUtil.Set("IsAutoLogin", value);
        }

        //从本地存储中获取到上一次使用的账号
        public string LastEmail
        {
            get
            {
                var res = PrefsKitUtil.PrefsUtil.Get("LastEmail", "");
                return PrefsKitUtil.PrefsUtil.Get("LastEmail", "");
            }
            // 默认为空
            set => PrefsKitUtil.PrefsUtil.Set("LastEmail", value);
        }

        public int LastCountryCodeIndex
        {
            get => PrefsKitUtil.PrefsUtil.Get("LastCountryCodeIndex", 0); // 默认为首个
            set => PrefsKitUtil.PrefsUtil.Set("LastCountryCodeIndex", value);
        }

        public string LastPhone
        {
            get => PrefsKitUtil.PrefsUtil.Get("LastPhone", ""); // 默认为空
            set => PrefsKitUtil.PrefsUtil.Set("LastPhone", value);
        }
        public string LastAccount
        {
            get => PrefsKitUtil.PrefsUtil.Get("LastAccount", ""); // 默认为空
            set => PrefsKitUtil.PrefsUtil.Set("LastAccount", value);
        }

        /// <summary>
        /// 验证码对应的token
        /// </summary>
        public Dictionary<string, string> Account2VerifyTokenMap { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 账号请求过登录验证码
        /// </summary>
        public Dictionary<string, bool> Account2HasLoginVerify { get; set; } = new Dictionary<string, bool>();

        /// <summary>
        /// 缓存的验证码
        /// </summary>
        public string CacheVerifyCode { get; set; }
        /// <summary>
        /// 登录对应的token
        /// </summary>
        public string LoginToken { get; set; }
        /// <summary>
        /// 自动登录对应的token
        /// </summary>
        public string AutoLoginToken
        {
            get => PrefsKitUtil.PrefsUtil.Get("AutoLoginToken", ""); // 默认为空
            set => PrefsKitUtil.PrefsUtil.Set("AutoLoginToken", value);
        }

        #region GMDebugCode,realse请删除

        public string FastLoginAccount
        {
            get => PrefsKitUtil.PrefsUtil.Get("FirstLoginAccount", "");
            set => PrefsKitUtil.PrefsUtil.Set("FirstLoginAccount", value);
        }

        public string FastLoginPassword
        {
            get => PrefsKitUtil.PrefsUtil.Get("FastLoginPassword", "");
            set => PrefsKitUtil.PrefsUtil.Set("FastLoginPassword", value);
        }

        #endregion

        protected override void OnInit()
        {
        }

        private void SetLoginMethodInternal(LoginMethod method, bool notify)
        {
            AccountType newAccountType = AccountType.None;
            CheckType newCheckType = CheckType.None;
            switch (method)
            {
                case LoginMethod.EmailWithPassword:
                    newAccountType = AccountType.Email;
                    newCheckType = CheckType.Password;
                    break;
                case LoginMethod.PhoneWithVerificationCode:
                    newAccountType = AccountType.PhoneNumber;
                    newCheckType = CheckType.Verification;
                    break;
                case LoginMethod.PhoneWithPassword:
                    newAccountType = AccountType.PhoneNumber;
                    newCheckType = CheckType.Password;
                    break;
            }

            // 直接设置本地存储
            PrefsKitUtil.PrefsUtil.Set("AccountType", (int)newAccountType);
            PrefsKitUtil.PrefsUtil.Set("CheckType", (int)newCheckType);

            // 如果需要，发送事件
            if (notify)
            {
                this.SendEvent(new ViewLoginMethodChangedEvent(method));
            }
        }
    }
}
