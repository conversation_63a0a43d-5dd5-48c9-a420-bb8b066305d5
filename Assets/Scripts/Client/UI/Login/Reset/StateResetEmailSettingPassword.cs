using System;
using Client.Event.UserEvent;
using Client.Model;
using Cysharp.Threading.Tasks;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;

namespace TFG.UI
{
    [Serializable]
    public class StateResetEmailSettingPassword : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        private bool IsFirstInputPassword = true;

        private UIStateBase TargetState =>
            IsFirstInputPassword switch
            {
                true => _uiLoginPanel.StateResetEmailSettingPassword,
                false => this
            };

        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateResetLayerState;
            set { }
        }

        public override async UniTask Enter(object data = null)
        {
            IsFirstInputPassword = true;
            _uiLoginPanel.UIBackToLastStateButtonComponent.InitAndShow();
            _uiLoginPanel.UIBackToLastStateButtonComponent.BackBtn.OnClickEvent += BackBtnOnOnClickEvent;
            await _uiLoginPanel.UISetPasswordComponent.InitAndShow();
            _uiLoginPanel.UISetPasswordComponent.InitLabel();
            _uiLoginPanel.UINextComponent.InitAndShow();
            //再次输入密码
            _uiLoginPanel.UINextComponent.NextButton.OnClickEvent += NextButtonOnOnClickEvent;
            this.RegisterEvent<ResetPasswordSuccessEvent>(OnResetPasswordSuccessEvent);
            this.RegisterEvent<ResetPasswordFailedEvent>(ResetPasswordFailedEvent);
            this.RegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
        }

        public override UniTask Exit()
        {
            IsFirstInputPassword = true;
            _uiLoginPanel.UIBackToLastStateButtonComponent.HideAndDeinit();
            _uiLoginPanel.UISetPasswordComponent.HideAndDeinit();
            _uiLoginPanel.UINextComponent.HideAndDeinit();
            _uiLoginPanel.UINextComponent.NextButton.ClearAllEvents();
            this.UnRegisterEvent<ResetPasswordSuccessEvent>(OnResetPasswordSuccessEvent);
            this.UnRegisterEvent<ResetPasswordFailedEvent>(ResetPasswordFailedEvent);
            this.UnRegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
            return UniTask.CompletedTask;
        }

        private void BackBtnOnOnClickEvent()
        {
            _uiLoginPanel.TmpPassword = "";
            _uiLoginPanel.UISetPasswordComponent.InitLabel();
            if (IsFirstInputPassword == false)
            {
                IsFirstInputPassword = true;
            }
            else
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetVerificationCodeWithEmail).Forget();
            }
        }
        private void OnResetPasswordSuccessEvent(ResetPasswordSuccessEvent msg)
        {
            _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetSuccess).Forget();
        }

        private void ResetPasswordFailedEvent(ResetPasswordFailedEvent msg)
        {
            //收到失败后才允许重试
            _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
        }

        private void NextButtonOnOnClickEvent()
        {
            switch (IsFirstInputPassword)
            {
                case true:
                    RegisterVerificationCodeWithEmailCheckInput().Forget();
                    break;
                case false:
                    CheckAndTryRegister().Forget();
                    break;
            }
        }

        private void OnViewAccountTypeChanged(ViewAccountTypeChangedEvent e)
        {
            if (e.AccountType == AccountType.Email)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetVerificationCodeWithEmail)
                    .Forget();
            }
            else if (e.AccountType == AccountType.PhoneNumber)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetVerificationCodeWithPhone)
                    .Forget();
            }

            _uiLoginPanel.PreferencesModel.LastPhone = "";
            _uiLoginPanel.PreferencesModel.LastEmail = "";
            _uiLoginPanel.PreferencesModel.LastCountryCodeIndex = 0;
        }

        private async UniTask RegisterVerificationCodeWithEmailCheckInput()
        {
            _uiLoginPanel.UINextComponent.NextButton.DisableBtn();
            try
            {
                if (!await _uiLoginPanel.ValidateSetPasswordAsync(_uiLoginPanel.UINextComponent.NextButton)) return;
                //再次校验密码
                _uiLoginPanel.TmpPassword = _uiLoginPanel.UISetPasswordComponent.Password;
                IsFirstInputPassword = false;
                _uiLoginPanel.UISetPasswordComponent.ResetInput();
                _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}::CheckInput]+{e.Message} ");
            }
        }

        private async UniTask CheckAndTryRegister()
        {
            _uiLoginPanel.UINextComponent.NextButton.DisableBtn();
            if (!string.Equals(_uiLoginPanel.UISetPasswordComponent.Password, _uiLoginPanel.TmpPassword))
            {
                var LocalizationSystem = _uiLoginPanel.LocalizationSystem;
                string error = LocalizationSystem.GetLocalizationContent("Error");
                string enterSamePassword = LocalizationSystem.GetLocalizationContent("DifferentPassword");
                string ok = LocalizationSystem.GetLocalizationContent("OK");
                //两次输入密码不一致
                //第一次肯定合法才可以到第二次
                await UIDialogHelper.ShowNormalStyle(error, enterSamePassword, true, false,confirmText:ok);
                _uiLoginPanel.UISetPasswordComponent.ResetInput();
                _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
                return;
            }
            var model = this.GetModel<ClientAccountPreferencesModel>();
            this.SendCommand(new ResetPasswordEmailCommand(_uiLoginPanel.TmpEmail,_uiLoginPanel.TmpPassword));
        }

        public override void OnUpdate()
        {
        }
    }
} 
