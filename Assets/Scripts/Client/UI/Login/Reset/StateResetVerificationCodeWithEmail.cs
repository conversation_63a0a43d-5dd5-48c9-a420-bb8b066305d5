using System;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;

namespace TFG.UI
{
    [Serializable]
    public class StateResetVerificationCodeWithEmail : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateResetLayerState;
            set { }
        }

        public override async UniTask Enter(object data = null)
        {
            _uiLoginPanel.UIBackToLastStateButtonComponent.InitAndShow(_uiLoginPanel.UIfsmManager,
                _uiLoginPanel.StateLanding);
            await _uiLoginPanel.UIEmailInputComponent.InitAndShow(_uiLoginPanel.TmpEmail);
            _uiLoginPanel.UIEmailInputComponent.AfterChecked += UIEmailInputComponentOnAfterChecked;
            _uiLoginPanel.UISendVerificationRequestComponent.InitAndShow("EmailResetVerification");
            await _uiLoginPanel.UIVerificationCodeInputComponent.InitAndShow();
            _uiLoginPanel.UINextComponent.InitAndShow();
            _uiLoginPanel.UINextComponent.NextButton.OnClickEvent += NextButtonOnOnClickEvent;
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent += SendVerificationRequestBtnOnOnClickEvent;
            this.RegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
        }
        public override UniTask Exit()
        {
            _uiLoginPanel.TmpEmail = _uiLoginPanel.UIEmailInputComponent.Email;
            _uiLoginPanel.UINextComponent.HideAndDeinit();
            _uiLoginPanel.UIVerificationCodeInputComponent.HideAndDeinit();
            _uiLoginPanel.UISendVerificationRequestComponent.HideAndDeinit();
            _uiLoginPanel.UIEmailInputComponent.HideAndDeinit();
            _uiLoginPanel.UIBackToLastStateButtonComponent.HideAndDeinit();
            _uiLoginPanel.UINextComponent.NextButton.ClearAllEvents();
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent -= SendVerificationRequestBtnOnOnClickEvent;
            this.UnRegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
            return UniTask.CompletedTask;
        }

        private void UIEmailInputComponentOnAfterChecked(EmailValidationResult obj)
        {
            if (obj is not EmailValidationResult.Valid)
            {
                _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.DisableBtn();
                return;
            }
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.EnableBtn();
        }

        private void SendVerificationRequestBtnOnOnClickEvent()
        {
            SendVerificationCode().Forget();
        }

        private async UniTask SendVerificationCode()
        {
            if (!await _uiLoginPanel.ValidateEmailAsync(_uiLoginPanel.UINextComponent.NextButton))
            {
                _uiLoginPanel.UISendVerificationRequestComponent.ForceResetCooldown();
                return;
            }

            this.SendCommand(new GetVerificationCodeResetPasswordCommand(
                _uiLoginPanel.UIEmailInputComponent.Email, _uiLoginPanel.TmpEmail, "", 0));
        }

        private void NextButtonOnOnClickEvent()
        {
            RegisterVerificationCodeWithEmailCheckInput().Forget();
        }



        private void OnViewAccountTypeChanged(ViewAccountTypeChangedEvent e)
        {
            if (e.AccountType == AccountType.Email)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetVerificationCodeWithEmail)
                    .Forget();
            }
            else if (e.AccountType == AccountType.PhoneNumber)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetVerificationCodeWithPhone)
                    .Forget();
            }

            _uiLoginPanel.PreferencesModel.LastPhone = "";
            _uiLoginPanel.PreferencesModel.LastEmail = "";
            _uiLoginPanel.PreferencesModel.LastCountryCodeIndex = 0;
        }

        private async UniTask RegisterVerificationCodeWithEmailCheckInput()
        {
            _uiLoginPanel.UINextComponent.NextButton.DisableBtn();
            var localizationSystem = GetArchitecture().GetSystem<LocalizationSystem>();
            _uiLoginPanel.TmpEmail = _uiLoginPanel.UIEmailInputComponent.Email;
            try
            {
                if (!await _uiLoginPanel.ValidateEmailAsync(_uiLoginPanel.UINextComponent.NextButton)) return;
                //先校验有没有发验证码
                var model = this.GetModel<ClientAccountPreferencesModel>();
                model.Account2VerifyTokenMap.TryGetValue(_uiLoginPanel.TmpEmail, out var token);
                token = token ?? "";
                if (token.IsNullOrEmpty())
                {
                    string errorTitle = localizationSystem.GetLocalizationContent("Error");
                    string okButtonText = localizationSystem.GetLocalizationContent("OK");
                    string PressSend = localizationSystem.GetLocalizationContent("PressSend");
                    await UIDialogHelper.ShowNormalStyle(errorTitle, PressSend, true, false, confirmText: okButtonText);
                    _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
                    return;
                }
                if (!await _uiLoginPanel.ValidateVerificationCodeAsync(_uiLoginPanel.UINextComponent.NextButton)) return;
                var code = _uiLoginPanel.UIVerificationCodeInputComponent.VerificationCode;
                var res = await this.SendCommandAsync(new VerifyVerificationCodeCommand(token,code));
                if (res.IsSuccess)
                {
                    LogKit.I($"[{GetType().FullName}::RegisterVerificationCodeWithPhoneCheckInput] VerifyVerificationCodeSuccess");
                    _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetEmailSettingPassword).Forget();
                }
                else
                {
                    string errorTitle = localizationSystem.GetLocalizationContent("Error");
                    string okButtonText = localizationSystem.GetLocalizationContent("OK");
                    string errorMsg = localizationSystem.GetLocalizationContent(res.MsgKey);
                    await UIDialogHelper.ShowNormalStyle(errorTitle, errorMsg, true, false, confirmText: okButtonText);
                    _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
                    return;
                }
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}::CheckInput]+{e.Message} ");
            }
        }

        public override void OnUpdate()
        {
        }
    }
} 
