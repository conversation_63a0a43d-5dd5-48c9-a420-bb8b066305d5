using System;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;
using UnityEngine;

namespace TFG.UI
{
    [Serializable]
    public class StateResetVerificationCodeWithPhone : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateResetLayerState;
            set { }
        }

        public override async UniTask Enter(object data = null)
        {
            _uiLoginPanel.UIBackToLastStateButtonComponent.InitAndShow(_uiLoginPanel.UIfsmManager,
                _uiLoginPanel.StateLanding);
            await _uiLoginPanel.UIPhoneNumberInputComponent.InitAndShow(_uiLoginPanel.TmpPhone,_uiLoginPanel);
            _uiLoginPanel.UISendVerificationRequestComponent.InitAndShow("PhoneResetVerification");
            await _uiLoginPanel.UIVerificationCodeInputComponent.InitAndShow();
            _uiLoginPanel.UINextComponent.InitAndShow();
            _uiLoginPanel.UINextComponent.NextButton.OnClickEvent += NextButtonOnOnClickEvent;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnPanelWillOpen += OnDropdownOpen;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnAfterPanelClose +=
                OnDropdownClose;
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent +=
                SendVerificationRequestBtnOnOnClickEvent;
            this.RegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
        }

        public override UniTask Exit()
        {
            _uiLoginPanel.TmpPhone = _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber;
            _uiLoginPanel.TmpCountryCodeIndex = _uiLoginPanel.UIPhoneNumberInputComponent.CountryCodeIndex;
            _uiLoginPanel.UINextComponent.HideAndDeinit();
            _uiLoginPanel.UIVerificationCodeInputComponent.HideAndDeinit();
            _uiLoginPanel.UISendVerificationRequestComponent.HideAndDeinit();
            _uiLoginPanel.UIPhoneNumberInputComponent.HideAndDeinit();
            _uiLoginPanel.UIBackToLastStateButtonComponent.HideAndDeinit();
            _uiLoginPanel.UINextComponent.NextButton.ClearAllEvents();
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnPanelWillOpen -= OnDropdownOpen;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnAfterPanelClose -=
                OnDropdownClose;
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent -=
                SendVerificationRequestBtnOnOnClickEvent;
            this.UnRegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
            return UniTask.CompletedTask;
        }

        private void SendVerificationRequestBtnOnOnClickEvent()
        {
            SendVerificationCode().Forget();
        }

        private async UniTask SendVerificationCode()
        {
            if (!await _uiLoginPanel.ValidatePhoneNumberAsync(_uiLoginPanel.UINextComponent.NextButton))
            {
                _uiLoginPanel.UISendVerificationRequestComponent.ForceResetCooldown();
                return;
            }
            this.SendCommand(new GetVerificationCodeResetPasswordCommand(
                _uiLoginPanel.UIPhoneNumberInputComponent.PhoneAccount, "",
                _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber, _uiLoginPanel.UIPhoneNumberInputComponent.CountryCodeIndex));
        }

        private void OnDropdownOpen()
        {
            _uiLoginPanel.UISendVerificationRequestComponent.CanvasGroup.DisableView();
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeRoot.alpha = 0;
            _uiLoginPanel.UIVerificationCodeInputComponent.CanvasGroup.DisableView();
            _uiLoginPanel.UINextComponent.CanvasGroup.DisableView();
        }

        private void OnDropdownClose()
        {
            _uiLoginPanel.UISendVerificationRequestComponent.CanvasGroup.EnableView();
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeRoot.alpha = 1;
            _uiLoginPanel.UIVerificationCodeInputComponent.CanvasGroup.EnableView();
            _uiLoginPanel.UINextComponent.CanvasGroup.EnableView();
        }

        private void NextButtonOnOnClickEvent()
        {
            RegisterVerificationCodeWithPhoneCheckInput().Forget();
        }

        private void OnViewAccountTypeChanged(ViewAccountTypeChangedEvent e)
        {
            if (e.AccountType == AccountType.Email)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetVerificationCodeWithEmail)
                    .Forget();
            }
            else if (e.AccountType == AccountType.PhoneNumber)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetVerificationCodeWithPhone)
                    .Forget();
            }

            _uiLoginPanel.PreferencesModel.LastPhone = "";
            _uiLoginPanel.PreferencesModel.LastEmail = "";
            _uiLoginPanel.PreferencesModel.LastCountryCodeIndex = 0;
        }

        private async UniTask RegisterVerificationCodeWithPhoneCheckInput()
        {
            _uiLoginPanel.UINextComponent.NextButton.DisableBtn();
            _uiLoginPanel.TmpCountryCodeIndex = _uiLoginPanel.UIPhoneNumberInputComponent.CountryCodeIndex;
            _uiLoginPanel.TmpCountryCode = _uiLoginPanel.UIPhoneNumberInputComponent.CountryCode;
            _uiLoginPanel.TmpPhone = _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber;
            var localizationSystem = GetArchitecture().GetSystem<LocalizationSystem>();
            try
            {
                if (!await _uiLoginPanel.ValidatePhoneNumberAsync(_uiLoginPanel.UINextComponent.NextButton))
                    return;
                //先校验有没有发验证码
                var model = this.GetModel<ClientAccountPreferencesModel>();
                model.Account2VerifyTokenMap.TryGetValue(_uiLoginPanel.TmpAccount, out var token);
                token = token ?? "";
                if (token.IsNullOrEmpty())
                {
                    string errorTitle = localizationSystem.GetLocalizationContent("Error");
                    string okButtonText = localizationSystem.GetLocalizationContent("OK");
                    string PressSend = localizationSystem.GetLocalizationContent("PressSend");
                    await UIDialogHelper.ShowNormalStyle(errorTitle, PressSend, true, false, confirmText: okButtonText);
                    _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
                    return;
                }
                if (!await _uiLoginPanel.ValidateVerificationCodeAsync(_uiLoginPanel.UINextComponent
                        .NextButton))
                    return;
                var code = _uiLoginPanel.UIVerificationCodeInputComponent.VerificationCode;
                var res = await this.SendCommandAsync(new VerifyVerificationCodeCommand(token,code));
                if (res.IsSuccess)
                {
                    LogKit.I($"[{GetType().FullName}::RegisterVerificationCodeWithPhoneCheckInput] VerifyVerificationCodeSuccess");
                    _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetPhoneSettingPassword)
                        .Forget();
                }
                else
                {
                    string errorTitle = localizationSystem.GetLocalizationContent("Error");
                    string okButtonText = localizationSystem.GetLocalizationContent("OK");
                    string errorMsg = localizationSystem.GetLocalizationContent(res.MsgKey);
                    await UIDialogHelper.ShowNormalStyle(errorTitle, errorMsg, true, false, confirmText: okButtonText);
                    _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
                    return;
                }
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}::CheckInput]+{e.Message} ");
            }
        }

        public override void OnUpdate()
        {
        }
    }
}
