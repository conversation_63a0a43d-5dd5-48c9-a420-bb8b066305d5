using System;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.Command;
using GameClient.Event.UserEvent;
using QFramework;
using TFG.UI.Common;
using UnityEngine;

namespace TFG.UI
{
    /*状态 3: 手机号 + 验证码登录 (State_LoginWithPhoneVerification)*/
    [Serializable]
    public class StateLoginWithPhoneVerification : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateLoginLayerState;
            set { }
        }

        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override async UniTask Enter(object data = null)
        {
            await _uiLoginPanel.UIPhoneNumberInputComponent.InitAndShow(_uiLoginPanel.TmpPhone,_uiLoginPanel);
            _uiLoginPanel.UISendVerificationRequestComponent.InitAndShow("PhoneLoginVerification");
            await _uiLoginPanel.UIVerificationCodeInputComponent.InitAndShow();
            _uiLoginPanel.UIChangePasswordCheckTypeComponent.InitAndShow();
            this.RegisterEvent<ViewLoginMethodChangedEvent>(OnLoginMethodChanged);
            this.RegisterEvent<LoginFailedEvent>(OnloginFailed);
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnPanelWillOpen += OnDropdownOpen;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnAfterPanelClose +=
                OnDropdownClose;
            _uiLoginPanel.UILoginComponent.LogInBtn.OnClickEvent += TryLogin;
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent +=
                SendVerificationRequestBtnOnOnClickEvent;
        }
        private void OnloginFailed(LoginFailedEvent obj)
        {
            _uiLoginPanel.UILoginComponent.LogInBtn.EnableBtn();
        }

        private void SendVerificationRequestBtnOnOnClickEvent()
        {
            SendVerificationCode().Forget();
        }

        private async UniTask SendVerificationCode()
        {
            if (!await _uiLoginPanel.ValidatePhoneNumberAsync(_uiLoginPanel.UILoginComponent.LogInBtn))
            {
                _uiLoginPanel.UISendVerificationRequestComponent.ForceResetCooldown();
                return;
            }
            this.SendCommand(new GetVerificationCodeLoginCommand(
                _uiLoginPanel.UIPhoneNumberInputComponent.PhoneAccount, "",
                _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber, _uiLoginPanel.UIPhoneNumberInputComponent.CountryCodeIndex));
        }

        public override UniTask Exit()
        {
            _uiLoginPanel.TmpPhone = _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber;
            _uiLoginPanel.TmpCountryCodeIndex = _uiLoginPanel.UIPhoneNumberInputComponent.CountryCodeIndex;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnPanelWillOpen -= OnDropdownOpen;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnAfterPanelClose -=
                OnDropdownClose;
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent -=
                SendVerificationRequestBtnOnOnClickEvent;
            this.UnRegisterEvent<ViewLoginMethodChangedEvent>(OnLoginMethodChanged);
            this.UnRegisterEvent<LoginFailedEvent>(OnloginFailed);
            _uiLoginPanel.UIPhoneNumberInputComponent.HideAndDeinit();
            _uiLoginPanel.UISendVerificationRequestComponent.HideAndDeinit();
            _uiLoginPanel.UIVerificationCodeInputComponent.HideAndDeinit();
            _uiLoginPanel.UIChangePasswordCheckTypeComponent.HideAndDeinit();
            _uiLoginPanel.UILoginComponent.LogInBtn.ClearAllEvents();
            return UniTask.CompletedTask;
        }

        private void OnDropdownOpen()
        {
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeRoot.alpha = 0;
            _uiLoginPanel.UISendVerificationRequestComponent.CanvasGroup.DisableView();
            _uiLoginPanel.UIVerificationCodeInputComponent.CanvasGroup.DisableView();
            _uiLoginPanel.UILoginComponent.CanvasGroup.DisableView();
        }

        private void OnDropdownClose()
        {
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeRoot.alpha = 1;
            _uiLoginPanel.UISendVerificationRequestComponent.CanvasGroup.EnableView();
            _uiLoginPanel.UIVerificationCodeInputComponent.CanvasGroup.EnableView();
            _uiLoginPanel.UILoginComponent.CanvasGroup.EnableView();
        }
        

        private void OnLoginMethodChanged(ViewLoginMethodChangedEvent e)
        {
            if (e.LoginMethod == LoginMethod.EmailWithPassword)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateLoginWithEmailPassword).Forget();
            }
            else if (e.LoginMethod == LoginMethod.PhoneWithPassword)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateLoginWithPhonePassword).Forget();
            }
        }

        private void TryLogin()
        {
            OnLogin().Forget();
        }

        private async UniTask OnLogin()
        {
            _uiLoginPanel.UILoginComponent.LogInBtn.DisableBtn();
            var localizationSystem = GetArchitecture().GetSystem<LocalizationSystem>();
            try
            {
                if (!await _uiLoginPanel.ValidatePhoneNumberAsync(_uiLoginPanel.UILoginComponent.LogInBtn)) return;
                var model = this.GetModel<ClientAccountPreferencesModel>();
                model.Account2HasLoginVerify.TryGetValue(_uiLoginPanel.UIPhoneNumberInputComponent.PhoneAccount, out var hasVerify);
                //先校验有没有发验证码
                if (!hasVerify)
                {
                    string errorTitle = localizationSystem.GetLocalizationContent("Error");
                    string okButtonText = localizationSystem.GetLocalizationContent("OK");
                    string PressSend = localizationSystem.GetLocalizationContent("PressSend");
                    await UIDialogHelper.ShowNormalStyle(errorTitle, PressSend, true, false, confirmText: okButtonText);
                    _uiLoginPanel.UILoginComponent.LogInBtn.EnableBtn();
                    return;
                }
                if (!await _uiLoginPanel.ValidateVerificationCodeAsync(_uiLoginPanel.UILoginComponent.LogInBtn)) return;
                this.SendCommand(new PhoneVerificationCodeLoginCommand(_uiLoginPanel.UIPhoneNumberInputComponent.CountryCodeIndex,
                    _uiLoginPanel.UIPhoneNumberInputComponent.CountryCode,
                    _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber));
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}::OnLogin]+{e.Message}+{e.ToString()} ");
            }
        }

        public override void OnUpdate()
        {
        }
    }
} 
