using System;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;
using TFG.UI.Common.DialogSystem;
using UnityEngine;

namespace TFG.UI
{
    /*状态 1: 初始着陆页 (State_Landing)*/
    [Serializable]
    public class StateLanding : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        public CanvasGroup CanvasGroup;
        public UISmartButton LoginBtn;
        public UISmartButton RegisterBtn;
        public UISmartButton OptionsBtn;
        public UISmartButton ExitGameBtn;
        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;
        public override UIStateBase ParentState { get; set; } = null;

        public override UniTask Enter(object data = null)
        {
            RetryCount = 0;
            LoginBtn.OnClickEvent += LoginBtnOnOnClickEvent;
            RegisterBtn.OnClickEvent += RegisterBtnOnOnClickEvent;
            OptionsBtn.OnClickEvent += OptionsBtnOnOnClickEvent;
            ExitGameBtn.OnClickEvent += Application.Quit;
            _uiLoginPanel.ContentCanvasGroup.DisableView();
            CanvasGroup.EnableView();
            return UniTask.CompletedTask;
        }

        private void OptionsBtnOnOnClickEvent()
        {
            UIKit.OpenPanel<UISettings>().Forget();
        }

        private void RegisterBtnOnOnClickEvent()
        {
            _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterEntry).Forget();
        }

        private void LoginBtnOnOnClickEvent()
        {
            TryLogin().Forget();
        }

        private int RetryCount = 0;
        private async UniTask TryLogin()
        {
            if (_uiLoginPanel.PreferencesModel.IsAutoLogin)
            {
                var res = await this.SendCommandAsync<AutoLoginResult>(new AutoLoginCommand());
                if (!res.Success)
                {
                    var localizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
                    string errorTitle = localizationSystem.GetLocalizationContent("Error");
                    string retry = localizationSystem.GetLocalizationContent("Retry");
                    string back = localizationSystem.GetLocalizationContent("Back");
                    string key= res.MsgKey.IsNullOrEmpty()?"RequestError":res.MsgKey;
                    string errorMsg = localizationSystem.GetLocalizationContent(key);
                    var dialogResult = await UIDialogHelper.ShowNormalStyle(errorTitle, errorMsg, true, true,
                        confirmText: retry, cancelText: back);
                    if (dialogResult == DialogResult.Confirm)
                    {
                        RetryCount++;
                        if (RetryCount >= 3)
                        {
                            // 达到最大重试次数，弹出强制返回对话框
                            string error = localizationSystem.GetLocalizationContent("Error");
                            string maxRetriesMessage = localizationSystem.GetLocalizationContent("DialogRetryLimitation");
                            string okText = localizationSystem.GetLocalizationContent("OK");

                            await UIDialogHelper.ShowNormalStyle(error, maxRetriesMessage, true, false, confirmText: okText);
                            LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().IsAutoLogin = false;
                        }
                        else
                        {
                            TryLogin().Forget();
                        }
                    }
                    return;
                }
            }
            else
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateLoginEntry).Forget();
            }
        }

        public override void OnUpdate()
        {
        }

        public override UniTask Exit()
        {
            LoginBtn.ClearAllEvents();
            RegisterBtn.ClearAllEvents();
            ExitGameBtn.ClearAllEvents();
            CanvasGroup.DisableView();
            return UniTask.CompletedTask;
        }
    }
} 
