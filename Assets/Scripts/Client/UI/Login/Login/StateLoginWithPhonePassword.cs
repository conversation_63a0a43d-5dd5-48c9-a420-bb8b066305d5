using System;
using Client.Model;
using Cysharp.Threading.Tasks;
using GameClient.Command;
using GameClient.Event.UserEvent;
using QFramework;
using TFG.UI.Common;
using UnityEngine;

namespace TFG.UI
{
    /* 状态 4: 手机号 + 密码登录 (State_LoginWithPhonePassword)*/
    [Serializable]
    public class StateLoginWithPhonePassword : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateLoginLayerState;
            set { }
        }

        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override async UniTask Enter(object data = null)
        {
            await _uiLoginPanel.UIPhoneNumberInputComponent.InitAndShow(_uiLoginPanel.TmpPhone,_uiLoginPanel);
            await _uiLoginPanel.UILoginPasswordInputComponent.InitAndShow();
            _uiLoginPanel.UIChangeToResetPasswordComponent.InitAndShow(this._uiLoginPanel);
            _uiLoginPanel.UIChangeVerificationCheckTypeComponent.InitAndShow();
            this.RegisterEvent<ViewLoginMethodChangedEvent>(OnLoginMethodChanged);
            this.RegisterEvent<LoginFailedEvent>(OnloginFailed);
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnPanelWillOpen += OnDropdownOpen;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnAfterPanelClose +=
                OnDropdownClose;
            _uiLoginPanel.UILoginComponent.LogInBtn.OnClickEvent += TryLogin;
        }

        public override UniTask Exit()
        {
            _uiLoginPanel.TmpPhone = _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnPanelWillOpen -= OnDropdownOpen;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnAfterPanelClose -=
                OnDropdownClose;
            this.UnRegisterEvent<LoginFailedEvent>(OnloginFailed);
            this.UnRegisterEvent<ViewLoginMethodChangedEvent>(OnLoginMethodChanged);
            _uiLoginPanel.UIPhoneNumberInputComponent.HideAndDeinit();
            _uiLoginPanel.UILoginPasswordInputComponent.HideAndDeinit();
            _uiLoginPanel.UIChangeToResetPasswordComponent.HideAndDeinit();
            _uiLoginPanel.UIChangeVerificationCheckTypeComponent.HideAndDeinit();
            _uiLoginPanel.UILoginComponent.LogInBtn.ClearAllEvents();
            return UniTask.CompletedTask;
        }
        private void OnloginFailed(LoginFailedEvent obj)
        {
            _uiLoginPanel.UILoginComponent.LogInBtn.EnableBtn();
        }

        private void OnDropdownOpen()
        {
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeRoot.alpha = 0;
            _uiLoginPanel.UILoginPasswordInputComponent.CanvasGroup.DisableView();
            _uiLoginPanel.UILoginComponent.CanvasGroup.DisableView();
        }

        private void OnDropdownClose()
        {
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeRoot.alpha = 1;
            _uiLoginPanel.UILoginPasswordInputComponent.CanvasGroup.EnableView();
            _uiLoginPanel.UILoginComponent.CanvasGroup.EnableView();
        }

        private void OnLoginMethodChanged(ViewLoginMethodChangedEvent e)
        {
            if (e.LoginMethod == LoginMethod.EmailWithPassword)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateLoginWithEmailPassword).Forget();
            }
            else if (e.LoginMethod == LoginMethod.PhoneWithVerificationCode)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateLoginWithPhoneVerification)
                    .Forget();
            }
        }

        private void TryLogin()
        {
            OnLogin().Forget();
        }

        private async UniTask OnLogin()
        {
            _uiLoginPanel.UILoginComponent.LogInBtn.DisableBtn();
            try
            {
                if (!await _uiLoginPanel.ValidatePhoneNumberAsync(_uiLoginPanel.UILoginComponent.LogInBtn)) return;
                if (!await _uiLoginPanel.ValidateLoginPasswordAsync(_uiLoginPanel.UILoginComponent.LogInBtn)) return;
                this.SendCommand(new PhonePasswordLoginCommand(
                    _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.CurrentIndex,
                    _uiLoginPanel.UIPhoneNumberInputComponent.CountryCode,
                    _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber,
                    _uiLoginPanel.UILoginPasswordInputComponent.Password));
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}::OnLogin]+{e.Message} ");
            }
        }

        public override void OnUpdate()
        {
        }
    }
} 
