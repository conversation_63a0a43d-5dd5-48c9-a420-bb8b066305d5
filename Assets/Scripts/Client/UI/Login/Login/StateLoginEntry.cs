using System;
using Client.Model;
using Cysharp.Threading.Tasks;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;

namespace TFG.UI
{
    // 状态 1.5: 登录通用UI入口 (State_Login_Entry)
    [Serializable]
    public class StateLoginEntry : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateLoginLayerState;
            set { }
        }

        public override UniTask Enter(object data = null)
        {
            switch (_uiLoginPanel.PreferencesModel.AccountType)
            {
                default:
                case AccountType.None:
                    _uiLoginPanel.TmpEmail = "";
                    _uiLoginPanel.TmpPhone = "";
                    _uiLoginPanel.TmpCountryCodeIndex = 0;
                    break;
                case AccountType.PhoneNumber:
                    _uiLoginPanel.TmpEmail = "";
                    _uiLoginPanel.TmpPhone = _uiLoginPanel.PreferencesModel.LastPhone.IsNotNullAndEmpty()?_uiLoginPanel.PreferencesModel.LastPhone:"";
                    _uiLoginPanel.TmpCountryCodeIndex =  _uiLoginPanel.PreferencesModel.LastCountryCodeIndex;
                    break;
                case AccountType.Email:
                    _uiLoginPanel.TmpEmail = _uiLoginPanel.PreferencesModel.LastEmail.IsNotNullAndEmpty()?_uiLoginPanel.PreferencesModel.LastEmail:"";
                    _uiLoginPanel.TmpPhone = "";
                    _uiLoginPanel.TmpCountryCodeIndex = 0;
                    break;
                
            }
            SwitchToNextStateAsync().Forget();
            return UniTask.CompletedTask;
        }

        // 这是一个新的、独立的异步流程
        private async UniTaskVoid SwitchToNextStateAsync()
        {
            // 等待一帧，确保第一个ChangeState流程已完全结束
            await UniTask.Yield();

            // 此刻，_isTransitioning锁必定已经释放，可以安全地发起下一次切换
            switch (_uiLoginPanel.PreferencesModel.LoginMethod)
            {
                case LoginMethod.PhoneWithPassword:
                    _uiLoginPanel.PreferencesModel.LastEmail = "";
                    _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateLoginWithPhonePassword)
                        .Forget();
                    break;
                case LoginMethod.PhoneWithVerificationCode:
                    _uiLoginPanel.PreferencesModel.LastEmail = "";
                    _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateLoginWithPhoneVerification)
                        .Forget();
                    break;
                case LoginMethod.EmailWithPassword:
                default:
                    _uiLoginPanel.PreferencesModel.LastCountryCodeIndex = 0;
                    _uiLoginPanel.PreferencesModel.LastPhone = "";
                    _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateLoginWithEmailPassword)
                        .Forget();
                    break;
            }
        }

        // 这是一个瞬时状态，无需实现Exit和OnUpdate
        public override UniTask Exit() => UniTask.CompletedTask;

        public override void OnUpdate()
        {
        }
    }
} 
