using System;
using Client.Model;
using Cysharp.Threading.Tasks;
using GameClient.Command;
using GameClient.Event.UserEvent;
using QFramework;
using TFG.UI.Common;

namespace TFG.UI
{
    /*状态 2: 邮箱登录 (State_LoginWithEmail)*/
    [Serializable]
    public class StateLoginWithEmailPassword : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateLoginLayerState;
            set { }
        }

        public override async UniTask Enter(object data = null)
        {
            await _uiLoginPanel.UIEmailInputComponent.InitAndShow(_uiLoginPanel.TmpEmail);
            await _uiLoginPanel.UILoginPasswordInputComponent.InitAndShow();
            _uiLoginPanel.UIChangeToResetPasswordComponent.InitAndShow(this._uiLoginPanel);
            _uiLoginPanel.UILoginComponent.LogInBtn.OnClickEvent += TryLogin;
            this.RegisterEvent<ViewLoginMethodChangedEvent>(OnLoginMethodChanged);
            this.RegisterEvent<LoginFailedEvent>(OnloginFailed);
        }

        private void OnloginFailed(LoginFailedEvent obj)
        {
            _uiLoginPanel.UILoginComponent.LogInBtn.EnableBtn();
        }

        public override UniTask Exit()
        {
            _uiLoginPanel.TmpEmail = _uiLoginPanel.UIEmailInputComponent.Email;
            this.UnRegisterEvent<LoginFailedEvent>(OnloginFailed);
            this.UnRegisterEvent<ViewLoginMethodChangedEvent>(OnLoginMethodChanged);
            _uiLoginPanel.UIEmailInputComponent.HideAndDeinit();
            _uiLoginPanel.UILoginPasswordInputComponent.HideAndDeinit();
            _uiLoginPanel.UIChangeToResetPasswordComponent.HideAndDeinit();
            _uiLoginPanel.UILoginComponent.LogInBtn.ClearAllEvents();
            return UniTask.CompletedTask;
        }

        private void OnLoginMethodChanged(ViewLoginMethodChangedEvent e)
        {
            // 如果新的登录方式不是自己，就切换状态
            if (e.LoginMethod != LoginMethod.EmailWithPassword)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateLoginWithPhoneVerification).Forget();
            }
        }

        private void TryLogin()
        {
            OnLogin().Forget();
        }

        private async UniTask OnLogin()
        {
            _uiLoginPanel.UILoginComponent.LogInBtn.DisableBtn();
            try
            {
                if (!await _uiLoginPanel.ValidateEmailAsync(_uiLoginPanel.UILoginComponent.LogInBtn)) return;
                if (!await _uiLoginPanel.ValidateLoginPasswordAsync(_uiLoginPanel.UILoginComponent.LogInBtn)) return;
                this.SendCommand(new EmailPasswordLoginCommand(_uiLoginPanel.UIEmailInputComponent.Email,
                    _uiLoginPanel.UILoginPasswordInputComponent.Password));
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}::OnLogin]+{e.Message} ");
            }
        }

        public override void OnUpdate()
        {
        }
    }
} 
