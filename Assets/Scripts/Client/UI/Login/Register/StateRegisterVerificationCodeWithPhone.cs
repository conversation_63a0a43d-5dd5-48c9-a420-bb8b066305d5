using System;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;
using UnityEngine;

namespace TFG.UI
{
    [Serializable]
    public class StateRegisterVerificationCodeWithPhone : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateRegisterLayerState;
            set { }
        }

        public override async UniTask Enter(object data = null)
        {
            _uiLoginPanel.UIBackToLastStateButtonComponent.InitAndShow(_uiLoginPanel.UIfsmManager,
                _uiLoginPanel.StateLanding);
            await _uiLoginPanel.UIPhoneNumberInputComponent.InitAndShow("",
                _uiLoginPanel);
            _uiLoginPanel.UISendVerificationRequestComponent.InitAndShow("PhoneRegisterVerification");
            await _uiLoginPanel.UIVerificationCodeInputComponent.InitAndShow();
            _uiLoginPanel.UINextComponent.InitAndShow();
            _uiLoginPanel.UINextComponent.NextButton.OnClickEvent += NextButtonOnOnClickEvent;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnPanelWillOpen += OnDropdownOpen;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnAfterPanelClose +=
                OnDropdownClose;
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent +=
                SendVerificationRequestBtnOnOnClickEvent;
            this.RegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
        }

        public override UniTask Exit()
        {
            _uiLoginPanel.TmpPhone = _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber;
            _uiLoginPanel.TmpCountryCodeIndex = _uiLoginPanel.UIPhoneNumberInputComponent.CountryCodeIndex;
            _uiLoginPanel.UINextComponent.HideAndDeinit();
            _uiLoginPanel.UIVerificationCodeInputComponent.HideAndDeinit();
            _uiLoginPanel.UISendVerificationRequestComponent.HideAndDeinit();
            _uiLoginPanel.UIPhoneNumberInputComponent.HideAndDeinit();
            _uiLoginPanel.UIBackToLastStateButtonComponent.HideAndDeinit();
            _uiLoginPanel.UINextComponent.NextButton.ClearAllEvents();
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnPanelWillOpen -= OnDropdownOpen;
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeDropdown.OnAfterPanelClose -=
                OnDropdownClose;
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent -=
                SendVerificationRequestBtnOnOnClickEvent;
            this.UnRegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
            return UniTask.CompletedTask;
        }

        private void SendVerificationRequestBtnOnOnClickEvent()
        {
            SendVerificationCode().Forget();
        }

        private async UniTask SendVerificationCode()
        {
            if (!await _uiLoginPanel.ValidatePhoneNumberAsync(_uiLoginPanel.UINextComponent.NextButton))
            {
                _uiLoginPanel.UISendVerificationRequestComponent.ForceResetCooldown();
                return;
            }

            this.SendCommand(new GetVerificationCodeRegisterCommand(
                _uiLoginPanel.UIPhoneNumberInputComponent.PhoneAccount, "",
                _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber,
                _uiLoginPanel.UIPhoneNumberInputComponent.CountryCodeIndex));
        }

        private void OnDropdownOpen()
        {
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeRoot.alpha = 0;
            _uiLoginPanel.UISendVerificationRequestComponent.CanvasGroup.DisableView();
            _uiLoginPanel.UIVerificationCodeInputComponent.CanvasGroup.DisableView();
            _uiLoginPanel.UINextComponent.CanvasGroup.DisableView();
        }

        private void OnDropdownClose()
        {
            _uiLoginPanel.UIPhoneNumberInputComponent.UICountryCodeRoot.alpha = 1;
            _uiLoginPanel.UISendVerificationRequestComponent.CanvasGroup.EnableView();
            _uiLoginPanel.UIVerificationCodeInputComponent.CanvasGroup.EnableView();
            _uiLoginPanel.UINextComponent.CanvasGroup.EnableView();
        }

        private void NextButtonOnOnClickEvent()
        {
            RegisterVerificationCodeWithPhoneCheckInput().Forget();
        }

        private void OnViewAccountTypeChanged(ViewAccountTypeChangedEvent e)
        {
            _uiLoginPanel.PreferencesModel.LastPhone = "";
            _uiLoginPanel.PreferencesModel.LastEmail = "";
            _uiLoginPanel.PreferencesModel.LastCountryCodeIndex = 0;
            // 如果新的登录方式不是自己，就切换状态
            if (e.AccountType == AccountType.Email)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterVerificationCodeWithEmail)
                    .Forget();
            }
            else if (e.AccountType == AccountType.PhoneNumber)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterVerificationCodeWithPhone)
                    .Forget();
            }
        }

        private async UniTask RegisterVerificationCodeWithPhoneCheckInput()
        {
            _uiLoginPanel.UINextComponent.NextButton.DisableBtn();
            var localizationSystem = GetArchitecture().GetSystem<LocalizationSystem>();
            _uiLoginPanel.TmpPhone = _uiLoginPanel.UIPhoneNumberInputComponent.PhoneNumber;
            _uiLoginPanel.TmpCountryCodeIndex = _uiLoginPanel.UIPhoneNumberInputComponent.CountryCodeIndex;
            _uiLoginPanel.TmpCountryCode = _uiLoginPanel.UIPhoneNumberInputComponent.CountryCode;
            try
            {
                //格式校验
                if (!await _uiLoginPanel.ValidatePhoneNumberAsync(_uiLoginPanel.UINextComponent.NextButton))
                    return;
                var model = this.GetModel<ClientAccountPreferencesModel>();
                model.Account2VerifyTokenMap.TryGetValue(_uiLoginPanel.TmpAccount, out var token);
                //先校验有没有发验证码
                token = token ?? "";
                if (token.IsNullOrEmpty())
                {
                    string errorTitle = localizationSystem.GetLocalizationContent("Error");
                    string okButtonText = localizationSystem.GetLocalizationContent("OK");
                    string PressSend = localizationSystem.GetLocalizationContent("PressSend");
                    await UIDialogHelper.ShowNormalStyle(errorTitle, PressSend, true, false, confirmText: okButtonText);
                    _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
                    return;
                }
                if (!await _uiLoginPanel.ValidateVerificationCodeAsync(_uiLoginPanel.UINextComponent
                        .NextButton))
                    return;
                var code = this.GetModel<ClientAccountPreferencesModel>().CacheVerifyCode;
                var res = await this.SendCommandAsync(new VerifyVerificationCodeCommand(token,code));
                if (res.IsSuccess)
                {
                    LogKit.I($"[{GetType().FullName}::RegisterVerificationCodeWithPhoneCheckInput] VerifyVerificationCodeSuccess");
                    _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterSetPasswordNow)
                        .Forget();
                }
                else
                {
                    string errorTitle = localizationSystem.GetLocalizationContent("Error");
                    string okButtonText = localizationSystem.GetLocalizationContent("OK");
                    string errorMsg = localizationSystem.GetLocalizationContent(res.MsgKey);
                    await UIDialogHelper.ShowNormalStyle(errorTitle, errorMsg, true, false, confirmText: okButtonText);
                    _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
                    return;
                }
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}::CheckInput]+{e.Message} ");
            }
        }

        public override void OnUpdate()
        {
        }
    }
}
