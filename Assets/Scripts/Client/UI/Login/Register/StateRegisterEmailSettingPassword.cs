using System;
using Client.Event.UserEvent;
using Client.Model;
using Cysharp.Threading.Tasks;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;

namespace TFG.UI
{
    [Serializable]
    public class StateRegisterEmailSettingPassword : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        private bool IsFirstInputPassword = true;

        private UIStateBase TargetState =>
            IsFirstInputPassword switch
            {
                true => _uiLoginPanel.StateRegisterVerificationCodeWithEmail,
                false => this
            };

        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateRegisterLayerState;
            set { }
        }

        public override async UniTask Enter(object data = null)
        {
            IsFirstInputPassword = true;
            _uiLoginPanel.UIBackToLastStateButtonComponent.InitAndShow();
            _uiLoginPanel.UIBackToLastStateButtonComponent.BackBtn.OnClickEvent += BackBtnOnOnClickEvent;
            await _uiLoginPanel.UISetPasswordComponent.InitAndShow();
            _uiLoginPanel.UISetPasswordComponent.InitLabel();
            _uiLoginPanel.UINextComponent.InitAndShow();
            //再次输入密码
            _uiLoginPanel.UINextComponent.NextButton.OnClickEvent += NextButtonOnOnClickEvent;
            this.RegisterEvent<RegisterSuccessEvent>(OnRegisterSuccessEvent);
            this.RegisterEvent<RegisterFailedEvent>(OnRegisterFailedEvent);
            this.RegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
        }

        public override UniTask Exit()
        {
            IsFirstInputPassword = true;
            _uiLoginPanel.UIBackToLastStateButtonComponent.HideAndDeinit();
            _uiLoginPanel.UISetPasswordComponent.HideAndDeinit();
            _uiLoginPanel.UINextComponent.HideAndDeinit();
            _uiLoginPanel.UINextComponent.NextButton.ClearAllEvents();
            this.UnRegisterEvent<RegisterSuccessEvent>(OnRegisterSuccessEvent);
            this.UnRegisterEvent<RegisterFailedEvent>(OnRegisterFailedEvent);
            this.UnRegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
            return UniTask.CompletedTask;
        }

        private void BackBtnOnOnClickEvent()
        {
            _uiLoginPanel.TmpPassword = "";
            _uiLoginPanel.UISetPasswordComponent.InitLabel();
            if (IsFirstInputPassword == false)
            {
                IsFirstInputPassword = true;
            }
            else
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterVerificationCodeWithEmail).Forget();
            }
        }

        private void OnRegisterSuccessEvent(RegisterSuccessEvent msg)
        {
            _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterSuccess).Forget();
        }

        private void OnRegisterFailedEvent(RegisterFailedEvent msg)
        {
            //收到失败后才允许重试
            _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
        }

        private void NextButtonOnOnClickEvent()
        {
            switch (IsFirstInputPassword)
            {
                case true:
                    RegisterVerificationCodeWithEmailCheckInput().Forget();
                    break;
                case false:
                    CheckAndTryRegister().Forget();
                    break;
            }
        }

        private void OnViewAccountTypeChanged(ViewAccountTypeChangedEvent e)
        {
            // 如果新的登录方式不是自己，就切换状态
            if (e.AccountType == AccountType.Email)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterVerificationCodeWithEmail)
                    .Forget();
            }
            else if (e.AccountType == AccountType.PhoneNumber)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterVerificationCodeWithPhone)
                    .Forget();
            }

            _uiLoginPanel.PreferencesModel.LastPhone = "";
            _uiLoginPanel.PreferencesModel.LastEmail = "";
            _uiLoginPanel.PreferencesModel.LastCountryCodeIndex = 0;
        }

        private async UniTask RegisterVerificationCodeWithEmailCheckInput()
        {
            _uiLoginPanel.UINextComponent.NextButton.DisableBtn();
            try
            {
                if (!await _uiLoginPanel.ValidateSetPasswordAsync(_uiLoginPanel.UINextComponent.NextButton))
                    return;
                //再次校验密码
                _uiLoginPanel.TmpPassword = _uiLoginPanel.UISetPasswordComponent.Password;
                IsFirstInputPassword = false;
                _uiLoginPanel.UISetPasswordComponent.ResetInput();
                _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}::CheckInput]+{e.Message} ");
            }
        }

        private async UniTask CheckAndTryRegister()
        {
            _uiLoginPanel.UINextComponent.NextButton.DisableBtn();
            if (!string.Equals(_uiLoginPanel.UISetPasswordComponent.Password, _uiLoginPanel.TmpPassword))
            {
                var LocalizationSystem = _uiLoginPanel.LocalizationSystem;
                string error = LocalizationSystem.GetLocalizationContent("Error");
                string enterSamePassword = LocalizationSystem.GetLocalizationContent("DifferentPassword");
                string ok = LocalizationSystem.GetLocalizationContent("OK");
                //两次输入密码不一致
                //第一次肯定合法才可以到第二次
                await UIDialogHelper.ShowNormalStyle(error, enterSamePassword, true, false,confirmText:ok);
                _uiLoginPanel.UISetPasswordComponent.ResetInput();
                _uiLoginPanel.UINextComponent.NextButton.EnableBtn();
                return;
            }

            var model = this.GetModel<ClientAccountPreferencesModel>();
            var  res = await this.SendCommandAsync(new EmailRegisterAsyncCommand(_uiLoginPanel.TmpEmail, _uiLoginPanel.TmpPassword));
            if (res.Success)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterSuccess).Forget();
                return;
            }
            else
            {
                var LocalizationSystem = _uiLoginPanel.LocalizationSystem;
                string errorTitle = LocalizationSystem.GetLocalizationContent("Error");
                string okButtonText = LocalizationSystem.GetLocalizationContent("OK");
                string key= res.MsgKey.IsNullOrEmpty()?"RequestError":res.MsgKey;
                string errorMsg = LocalizationSystem.GetLocalizationContent(key);
                await UIDialogHelper.ShowNormalStyle(errorTitle, errorMsg, true, false,
                    confirmText: okButtonText);
                return;
            }
        }

        public override void OnUpdate()
        {
        }
    }
}
