using System;
using Client.Event.UserEvent;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;
using TFG.UI.Common.DialogSystem;

namespace TFG.UI
{
    [Serializable]
    public class StateRegisterSetPasswordNow : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateRegisterLayerState;
            set { }
        }

        public override UniTask Enter(object data = null)
        {
            var sys = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string dialogPasswordSetting = sys.GetLocalizationContent("DialogPasswordSetting");
            _uiLoginPanel.UILabelComponent.InitAndShow(dialogPasswordSetting);
            _uiLoginPanel.UIConfirmAndCancelComponent.InitAndShow();
            _uiLoginPanel.UIConfirmAndCancelComponent.ConfirmButton.OnClickEvent += ConfirmButtonOnOnClickEvent;
            _uiLoginPanel.UIConfirmAndCancelComponent.CancelButton.OnClickEvent += CancelButtonOnOnClickEvent;
            _uiLoginPanel.UIConfirmAndCancelComponent.ConfirmButton.EnableBtn();
            _uiLoginPanel.UIConfirmAndCancelComponent.CancelButton.EnableBtn();
            return UniTask.CompletedTask;
        }
        
        public override UniTask Exit()
        {
            _uiLoginPanel.UILabelComponent.HideAndDeinit();
            _uiLoginPanel.UIConfirmAndCancelComponent.HideAndDeinit();
            RetryCount = 0;
            return UniTask.CompletedTask;
        }

        private void CancelButtonOnOnClickEvent()
        {
            _uiLoginPanel.UIConfirmAndCancelComponent.ConfirmButton.DisableBtn();
            _uiLoginPanel.UIConfirmAndCancelComponent.CancelButton.DisableBtn();
            TryRegister().Forget();
        }

        private int RetryCount = 0;
        private async UniTask TryRegister()
        {
            var result = await this.SendCommandAsync(new PhoneRegisterWithNoPasswordAsyncCommand(
                _uiLoginPanel.TmpCountryCodeIndex,
                _uiLoginPanel.TmpCountryCode,
                _uiLoginPanel.TmpPhone));
            if (result.Success)
            {
                LogKit.I($"[{GetType().FullName}::RegisterVerificationCodeWithPhoneCheckInput] RegisterSuccess");
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterSuccess).Forget();
            }
            else
            {
                var localizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
                string errorTitle = localizationSystem.GetLocalizationContent("Error");
                string retry = localizationSystem.GetLocalizationContent("Retry");
                string back = localizationSystem.GetLocalizationContent("Back");
                string key= result.MsgKey.IsNullOrEmpty()?"RequestError":result.MsgKey;
                string errorMsg = localizationSystem.GetLocalizationContent(key);
                var res = await UIDialogHelper.ShowNormalStyle(errorTitle, errorMsg, true, true,
                    confirmText: retry, cancelText: back);
               if (res == DialogResult.Confirm)
               {
                   RetryCount++;
                   if (RetryCount >= 3)
                   {
                       // 达到最大重试次数，弹出强制返回对话框
                       string error = localizationSystem.GetLocalizationContent("Error");
                       string maxRetriesMessage = localizationSystem.GetLocalizationContent("DialogRetryLimitation");
                       string okText = localizationSystem.GetLocalizationContent("OK");

                       await UIDialogHelper.ShowNormalStyle(error, maxRetriesMessage, true, false, confirmText: okText);
                       
                       // 用户点击确认后，强制返回到初始注册页面
                       _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterVerificationCodeWithPhone).Forget();
                   }
                   else
                   {
                       TryRegister().Forget();
                   }
               }
               else if (res == DialogResult.Cancel)
               {
                   //回退开始注册的页面
                   _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterVerificationCodeWithPhone).Forget();
               }
               
            }
            
        }

        private void ConfirmButtonOnOnClickEvent()
        {
            _uiLoginPanel.UIConfirmAndCancelComponent.ConfirmButton.DisableBtn();
            _uiLoginPanel.UIConfirmAndCancelComponent.CancelButton.DisableBtn();
            _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateRegisterPhoneSettingPassword).Forget();
        }

        public override void OnUpdate()
        {
        }
    }
} 
