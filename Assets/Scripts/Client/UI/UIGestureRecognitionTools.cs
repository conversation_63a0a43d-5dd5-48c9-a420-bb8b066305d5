using System;
using System.Collections.Generic;
using GameClient;
using GameClient.Gesture;
using GameShare.Events;
using QFramework;
using TFGShare.Protocol;
using UnityEditor;
using UnityEngine;

namespace TFG.UI
{
    public class UIGestureRecognitionToolsData : UIPanelData
    {
    }

    public partial class UIGestureRecognitionTools : UIPanel, IController, ICanSendEvent
    {
        // 未使用的字段已删除

        // 状态管理已迁移到UIStateManager

        // 重构后的管理器
        private UIStateManager _uiStateManager;
        private PlaybackController _playbackController;
        private GestureConfigurationManager _configManager;
        private GestureRecognitionUIManager _recognitionUIManager;

        // 录制相关字段
        private bool _isRecording = false;
        private readonly List<MovementData> _recordedMovements = new();
        private readonly List<TimestampedMovementData> _recordedSequence = new();
        private string _currentGestureName = "NewGesture";

        // 系统引用
        private GestureRecognitionSystem _recognitionSystem;
        private bool _isRecognitionSystemInitialized = false;

        // 显示更新控制
        private float _lastDisplayUpdateTime = 0f;

        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
        
        protected override void OnInit(IUIData uiData = null)
        {
            mData = uiData as UIGestureRecognitionToolsData ?? new UIGestureRecognitionToolsData();
            try
            {
                // 初始化管理器
                InitializeManagers();

                // 绑定按钮事件
                BindButtonEvents();

                // 注册事件监听
                RegisterEventListeners();

                // 初始化系统引用
                InitializeSystems();

                // 设置初始UI状态
                _uiStateManager.SetState(UIStateManager.UIState.Default);

                LogKit.I($"[{GetType().FullName}::OnInit] UI重构完成 - 新的状态管理系统已启用");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnInit] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化管理器
        /// </summary>
        private void InitializeManagers()
        {
            _uiStateManager = new UIStateManager();
            _playbackController = new PlaybackController();
            _configManager = new GestureConfigurationManager();
            _recognitionUIManager = new GestureRecognitionUIManager();

            // 初始化UI状态管理器
            _uiStateManager.Initialize(startRecordingButton, playbackButton, recognitionButton, saveTemplateButton);

            // 绑定状态变化事件
            _uiStateManager.OnStateChanged += OnUIStateChanged;

            // 初始化关节点选择
            if (jointSelectionContainer != null)
            {
                _configManager.InitializeJointSelection(jointSelectionContainer);
            }
        }

        /// <summary>
        /// 绑定按钮事件
        /// </summary>
        private void BindButtonEvents()
        {
            // 新按钮事件
            startRecordingButton?.onClick.AddListener(OnStartRecordingClick);
            playbackButton?.onClick.AddListener(OnPlaybackClick);
            recognitionButton?.onClick.AddListener(OnRecognitionClick);
            saveTemplateButton?.onClick.AddListener(OnSaveTemplateClick);

            // 保留退出按钮事件
            btnExit?.onClick.AddListener(OnExitClick);
        }

        /// <summary>
        /// 注册事件监听
        /// </summary>
        private void RegisterEventListeners()
        {
            this.RegisterEvent<BattlePlayerMovementEvent>(OnBattlePlayerMovement);

            // 识别事件
            this.RegisterEvent<GestureRecognizedEvent>(OnGestureRecognized);
            this.RegisterEvent<GestureRecognitionUpdateEvent>(OnGestureRecognitionUpdate);
            this.RegisterEvent<GestureRecognitionStartedEvent>(OnGestureRecognitionStarted);
            this.RegisterEvent<GestureRecognitionStoppedEvent>(OnGestureRecognitionStopped);
        }

        /// <summary>
        /// 绑定扩展功能按钮事件（简化版）
        /// </summary>
        private void BindExtendedButtonEvents()
        {
            // 只绑定存在的组件
            if (headOrientationToggle != null)
            {
                headOrientationToggle.onValueChanged.AddListener(OnHeadOrientationToggleChanged);
            }
        }



        /// <summary>
        /// 初始化系统引用
        /// </summary>
        private void InitializeSystems()
        {
            // 初始化识别系统
            _recognitionSystem = this.GetSystem<GestureRecognitionSystem>();
            _isRecognitionSystemInitialized = (_recognitionSystem != null);

            if (_isRecognitionSystemInitialized)
            {
                LogKit.I($"[{GetType().FullName}::InitializeSystems] Recognition system initialized successfully");
            }
        }

        /// <summary>
        /// Update 方法，处理 S 键录制控制、B键停止识别和系统更新
        /// </summary>
        private void Update()
        {
            try
            {
                var currentState = _uiStateManager.CurrentState;

                // S键录制控制（录制状态下）
                if (currentState == UIStateManager.UIState.Recording)
                {
                    if (Input.GetKeyDown(KeyCode.S) && !_isRecording)
                    {
                        StartRecording();
                    }
                    else if (Input.GetKeyUp(KeyCode.S) && _isRecording)
                    {
                        StopRecording();

                        // 弹出确认框询问是否保存
                        bool shouldSave = EditorUtility.DisplayDialog("保存录制数据", "是否保存本次录制的数据？", "保存", "取消");

                        if (shouldSave)
                        {
                            SaveRecordingToJson();
                        }

                        _uiStateManager.SetState(UIStateManager.UIState.Default);
                    }
                }



                // 更新回放系统
                if (currentState == UIStateManager.UIState.Playback && _playbackController != null)
                {
                    _playbackController.UpdatePlayback();
                }

                // 更新识别系统
                if (currentState == UIStateManager.UIState.Recognition && _recognitionUIManager != null)
                {
                    _recognitionUIManager.UpdateDisplayTimers(Time.deltaTime);
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::Update] Error: {ex.Message}");
            }
        }



        protected override void OnClose()
        {
            try
            {
                LogKit.I($"[{GetType().FullName}::OnClose] Starting OnClose cleanup");

                // 注销事件监听
                UnregisterAllEvents();

                // 重置状态
                _isRecording = false;
                _currentGestureName = "NewGesture";

                LogKit.I($"[{GetType().FullName}::OnClose] OnClose cleanup completed successfully");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnClose] Critical error in OnClose: {ex.Message}");
            }
        }

        /// <summary>
        /// 注销所有事件监听
        /// </summary>
        private void UnregisterAllEvents()
        {
            try
            {
                this.UnRegisterEvent<BattlePlayerMovementEvent>(OnBattlePlayerMovement);
                this.UnRegisterEvent<GestureRecognizedEvent>(OnGestureRecognized);
                this.UnRegisterEvent<GestureRecognitionUpdateEvent>(OnGestureRecognitionUpdate);
                this.UnRegisterEvent<GestureRecognitionStartedEvent>(OnGestureRecognitionStarted);
                this.UnRegisterEvent<GestureRecognitionStoppedEvent>(OnGestureRecognitionStopped);
                LogKit.I($"[{GetType().FullName}::UnregisterAllEvents] All events unregistered");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::UnregisterAllEvents] Error unregistering events: {ex.Message}");
            }
        }

        // 新的按钮事件处理

        /// <summary>
        /// 开始录制按钮点击事件
        /// </summary>
        private void OnStartRecordingClick()
        {
            try
            {
                _uiStateManager.SetState(UIStateManager.UIState.Recording);
                _recordedMovements.Clear();
                _recordedSequence.Clear();
                LogKit.I($"[{GetType().FullName}::OnStartRecordingClick] 进入录制待机状态，按住S键开始录制");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnStartRecordingClick] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 回放按钮点击事件
        /// </summary>
        private void OnPlaybackClick()
        {
            try
            {
                if (_uiStateManager.CurrentState == UIStateManager.UIState.Playback)
                {
                    // 停止回放逻辑
                    _playbackController.StopPlayback();
                    _uiStateManager.SetState(UIStateManager.UIState.Default);
                    LogKit.I($"[{GetType().FullName}::OnPlaybackClick] 停止回放");
                }
                else
                {
                    // 开始回放逻辑
                    var jsonPath = EditorUtility.OpenFilePanel("选择录制文件", "", "json");
                    if (string.IsNullOrEmpty(jsonPath))
                    {
                        // 用户取消选择，保持默认状态
                        LogKit.I($"[{GetType().FullName}::OnPlaybackClick] 用户取消文件选择");
                        _uiStateManager.SetState(UIStateManager.UIState.Default);
                        return;
                    }

                    // 验证文件存在性
                    if (!System.IO.File.Exists(jsonPath))
                    {
                        LogKit.E($"[{GetType().FullName}::OnPlaybackClick] 文件不存在: {jsonPath}");
                        _uiStateManager.SetState(UIStateManager.UIState.Default);
                        return;
                    }

                    // 开始回放
                    if (_playbackController.StartPlayback(jsonPath))
                    {
                        _uiStateManager.SetState(UIStateManager.UIState.Playback);
                        LogKit.I($"[{GetType().FullName}::OnPlaybackClick] 开始回放文件: {jsonPath}");
                    }
                    else
                    {
                        _uiStateManager.SetState(UIStateManager.UIState.Default);
                        LogKit.E($"[{GetType().FullName}::OnPlaybackClick] 回放失败: {jsonPath}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnPlaybackClick] Error: {ex.Message}");
                _uiStateManager.SetState(UIStateManager.UIState.Default);
            }
        }

        /// <summary>
        /// 识别按钮点击事件
        /// </summary>
        private void OnRecognitionClick()
        {
            try
            {
                if (_uiStateManager.CurrentState == UIStateManager.UIState.Recognition)
                {
                    // 停止识别逻辑
                    if (_recognitionSystem != null && _recognitionSystem.IsRecognitionActive)
                    {
                        _recognitionSystem.StopRecognition();

                        // 清理识别显示列表
                        ClearRecognitionDisplayItems();

                        // 清理识别UI管理器状态
                        if (_recognitionUIManager != null)
                        {
                            _recognitionUIManager.ClearAll();
                        }
                    }

                    // 隐藏识别列表
                    if (poseTemplateList != null)
                    {
                        poseTemplateList.gameObject.SetActive(false);
                    }

                    _uiStateManager.SetState(UIStateManager.UIState.Default);
                    LogKit.I($"[{GetType().FullName}::OnRecognitionClick] 停止识别");
                }
                else
                {
                    // 开始识别逻辑
                    var choice = EditorUtility.DisplayDialogComplex("选择识别文件类型", "请选择要加载的文件类型：", "单个dat文件", "dat文件目录", "取消");

                    var selectedPath = "";
                    var isDirectory = false;

                    switch (choice)
                    {
                        case 0: // 单个dat文件
                            selectedPath = EditorUtility.OpenFilePanel("选择dat文件", "", "dat");
                            isDirectory = false;
                            break;

                        case 1: // dat文件目录
                            selectedPath = EditorUtility.OpenFolderPanel("选择dat文件目录", "", "");
                            isDirectory = true;
                            break;

                        case 2: // 取消
                        default:
                            // 用户取消选择，保持默认状态
                            _uiStateManager.SetState(UIStateManager.UIState.Default);
                            return;
                    }

                    // 检查用户是否取消了文件/目录选择
                    if (string.IsNullOrEmpty(selectedPath))
                    {
                        // 用户取消选择，保持默认状态
                        _uiStateManager.SetState(UIStateManager.UIState.Default);
                        return;
                    }

                    // 加载识别文件并开始识别
                    LoadRecognitionFiles(selectedPath, isDirectory);
                    _uiStateManager.SetState(UIStateManager.UIState.Recognition);

                    // 开始识别
                    if (_recognitionSystem != null)
                    {
                        _recognitionSystem.StartRecognition();
                    }

                    var fileType = isDirectory ? "目录" : "文件";
                    LogKit.I($"[{GetType().FullName}::OnRecognitionClick] 开始识别，加载{fileType}: {selectedPath}");
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnRecognitionClick] Error: {ex.Message}");
                _uiStateManager.SetState(UIStateManager.UIState.Default);
            }
        }

        /// <summary>
        /// 保存模版按钮点击事件
        /// </summary>
        private void OnSaveTemplateClick()
        {
            try
            {
                // 显示关键点选择界面
                if (jointSelectionContainer != null)
                {
                    jointSelectionContainer.gameObject.SetActive(true);
                }

                // 获取当前回放数据
                var playbackData = _playbackController.GetCurrentPlaybackData();
                if (playbackData == null || playbackData.Count == 0)
                {
                    LogKit.W($"[{GetType().FullName}::OnSaveTemplateClick] 没有可用的回放数据");
                    // 没有数据时保持当前状态，不需要回退
                    return;
                }

                // 弹出保存对话框
                var savePath = EditorUtility.SaveFilePanel("保存手势模版", "", "gesture", "dat");
                if (string.IsNullOrEmpty(savePath))
                {
                    // 用户取消保存，保持当前Playback状态
                    LogKit.I($"[{GetType().FullName}::OnSaveTemplateClick] 用户取消保存操作");
                    return;
                }

                // 获取选择的关节点
                var selectedJoints = GetSelectedJoints();
                var useHeadOrientation = headOrientationToggle?.isOn ?? false;

                // 保存dat文件
                var gestureName = System.IO.Path.GetFileNameWithoutExtension(savePath);
                var result = DatFileManager.SaveDatFile(gestureName, playbackData, selectedJoints, useHeadOrientation);

                if (!string.IsNullOrEmpty(result))
                {
                    LogKit.I($"[{GetType().FullName}::OnSaveTemplateClick] 模版已保存: {savePath}, Result: {result}");
                }
                else
                {
                    LogKit.E($"[{GetType().FullName}::OnSaveTemplateClick] 保存模版失败: {savePath}");
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnSaveTemplateClick] Error: {ex.Message}");
                // 发生异常时保持当前状态，不强制回退到Default
            }
        }



        private void OnExitClick()
        {
            CloseSelf();
        }


        // 兼容方法已删除，使用新按钮系统


        // 辅助方法

        /// <summary>
        /// UI状态变化事件处理
        /// </summary>
        /// <param name="newState">新状态</param>
        private void OnUIStateChanged(UIStateManager.UIState newState)
        {
            try
            {
                // 根据状态更新UI显示
                switch (newState)
                {
                    case UIStateManager.UIState.Default:
                        // 隐藏关节选择界面
                        if (jointSelectionContainer != null)
                        {
                            jointSelectionContainer.gameObject.SetActive(false);
                        }
                        break;

                    case UIStateManager.UIState.Recording:
                        // 录制状态的特殊处理
                        break;

                    case UIStateManager.UIState.Playback:
                        // 回放状态的特殊处理
                        break;

                    case UIStateManager.UIState.Recognition:
                        // 识别状态的特殊处理
                        UpdateRecognitionDisplay();
                        break;
                }

                // 更新状态文本
                if (textTemplate != null)
                {
                    textTemplate.text = UIStateManager.GetStateDescription(newState);
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnUIStateChanged] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取选择的关节点
        /// </summary>
        /// <returns>选择的关节点列表</returns>
        private List<TrajectoryExtractor.JointIndex> GetSelectedJoints()
        {
            try
            {
                // 从GestureConfigurationManager获取选择的关节点
                if (_configManager != null)
                {
                    var selectedJoints = _configManager.GetSelectedTrajectoryJoints();

                    if (selectedJoints.Count > 0)
                    {
                        LogKit.I($"[{GetType().FullName}::GetSelectedJoints] Retrieved {selectedJoints.Count} selected joints from configuration");
                        return selectedJoints;
                    }
                }

                // 如果配置管理器不可用或没有选择的关节点，返回默认的五个关键关节点
                var defaultJoints = new List<TrajectoryExtractor.JointIndex>
                {
                    TrajectoryExtractor.JointIndex.Head,
                    TrajectoryExtractor.JointIndex.LeftHand,
                    TrajectoryExtractor.JointIndex.RightHand,
                    TrajectoryExtractor.JointIndex.LeftFoot,
                    TrajectoryExtractor.JointIndex.RightFoot
                };

                LogKit.I($"[{GetType().FullName}::GetSelectedJoints] Using default joints: {defaultJoints.Count}");
                return defaultJoints;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::GetSelectedJoints] Error: {ex.Message}");

                // 发生错误时返回默认关节点
                return new List<TrajectoryExtractor.JointIndex>
                {
                    TrajectoryExtractor.JointIndex.Head,
                    TrajectoryExtractor.JointIndex.LeftHand,
                    TrajectoryExtractor.JointIndex.RightHand,
                    TrajectoryExtractor.JointIndex.LeftFoot,
                    TrajectoryExtractor.JointIndex.RightFoot
                };
            }
        }

        /// <summary>
        /// 加载识别文件
        /// </summary>
        /// <param name="path">文件或目录路径</param>
        /// <param name="isDirectory">是否为目录</param>
        private void LoadRecognitionFiles(string path, bool isDirectory = true)
        {
            try
            {
                // 清理缓存
                DatFileManager.ClearCache();

                if (isDirectory)
                {
                    // 目录模式：加载目录中的所有dat文件
                    LoadRecognitionFilesFromDirectory(path);
                }
                else
                {
                    // 单文件模式：加载指定的dat文件
                    LoadSingleRecognitionFile(path);
                }

                // 使用识别系统加载模板
                if (_recognitionSystem != null)
                {
                    _recognitionSystem.LoadGestureTemplates();
                }

                // 初始化识别UI管理器的模板列表
                if (_recognitionUIManager != null && poseTemplateList?.content != null)
                {
                    _recognitionUIManager.CreateTemplateListItems(poseTemplateList.content);
                }

                LogKit.I($"[{GetType().FullName}::LoadRecognitionFiles] Recognition files loaded and UI initialized");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::LoadRecognitionFiles] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 从目录加载识别文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        private void LoadRecognitionFilesFromDirectory(string directoryPath)
        {
            try
            {
                if (!System.IO.Directory.Exists(directoryPath))
                {
                    LogKit.W($"[{GetType().FullName}::LoadRecognitionFilesFromDirectory] Directory not found: {directoryPath}");
                    return;
                }

                var datFiles = System.IO.Directory.GetFiles(directoryPath, "*.dat");

                if (datFiles.Length == 0)
                {
                    LogKit.W($"[{GetType().FullName}::LoadRecognitionFilesFromDirectory] No dat files found in directory: {directoryPath}");
                    return;
                }

                foreach (var filePath in datFiles)
                {
                    LoadSingleRecognitionFile(filePath);
                }

                LogKit.I($"[{GetType().FullName}::LoadRecognitionFilesFromDirectory] Loaded {datFiles.Length} dat files from directory");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::LoadRecognitionFilesFromDirectory] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载单个识别文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        private void LoadSingleRecognitionFile(string filePath)
        {
            try
            {
                if (!System.IO.File.Exists(filePath))
                {
                    LogKit.W($"[{GetType().FullName}::LoadSingleRecognitionFile] File not found: {filePath}");
                    return;
                }

                if (!filePath.EndsWith(".dat", System.StringComparison.OrdinalIgnoreCase))
                {
                    LogKit.W($"[{GetType().FullName}::LoadSingleRecognitionFile] File is not a dat file: {filePath}");
                    return;
                }

                // 记录文件加载信息（实际加载由识别系统处理）
                var fileName = System.IO.Path.GetFileNameWithoutExtension(filePath);
                LogKit.I($"[{GetType().FullName}::LoadSingleRecognitionFile] Queued dat file for loading: {fileName}");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::LoadSingleRecognitionFile] Error loading file {filePath}: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新识别显示
        /// </summary>
        private void UpdateRecognitionDisplay()
        {
            try
            {
                if (poseTemplateList == null)
                {
                    LogKit.W($"[{GetType().FullName}::UpdateRecognitionDisplay] poseTemplateList is null");
                    return;
                }

                poseTemplateList.gameObject.SetActive(true);

                // 获取识别UI管理器的相似度数据
                if (_recognitionUIManager == null)
                {
                    LogKit.W($"[{GetType().FullName}::UpdateRecognitionDisplay] _recognitionUIManager is null");
                    return;
                }

                // 获取当前相似度数据
                var similarities = _recognitionUIManager.GetCurrentSimilarities();
                var loadedTemplates = _recognitionUIManager.GetLoadedTemplates();

                // 清理现有的显示项
                ClearRecognitionDisplayItems();

                // 为每个加载的模板创建显示项
                foreach (var templateName in loadedTemplates)
                {
                    var similarity = similarities.GetValueOrDefault(templateName, 0f);
                    var isHighlighted = _recognitionUIManager.IsTemplateHighlighted(templateName);
                    CreateRecognitionDisplayItem(templateName, similarity, isHighlighted);
                }

                LogKit.I($"[{GetType().FullName}::UpdateRecognitionDisplay] Updated display for {loadedTemplates.Count} templates");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::UpdateRecognitionDisplay] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理识别显示项
        /// </summary>
        private void ClearRecognitionDisplayItems()
        {
            try
            {
                if (poseTemplateList?.content == null)
                {
                    return;
                }

                // 清理所有子对象
                for (int i = poseTemplateList.content.childCount - 1; i >= 0; i--)
                {
                    var child = poseTemplateList.content.GetChild(i);
                    if (child != null)
                    {
                        UnityEngine.Object.DestroyImmediate(child.gameObject);
                    }
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::ClearRecognitionDisplayItems] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建识别显示项
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <param name="similarity">相似度</param>
        /// <param name="isHighlighted">是否高亮</param>
        private void CreateRecognitionDisplayItem(string templateName, float similarity, bool isHighlighted)
        {
            try
            {
                if (poseTemplateList?.content == null)
                {
                    return;
                }

                // 创建显示项GameObject
                var itemGO = new GameObject($"RecognitionItem_{templateName}");
                itemGO.transform.SetParent(poseTemplateList.content, false);

                // 添加RectTransform组件
                var rectTransform = itemGO.AddComponent<RectTransform>();
                rectTransform.sizeDelta = new Vector2(200, 30);

                // 添加文本组件
                var textComponent = itemGO.AddComponent<TMPro.TextMeshProUGUI>();
                textComponent.text = $"{templateName}: {similarity:F3}";
                textComponent.fontSize = 14;
                textComponent.color = isHighlighted ? Color.green : Color.white;
                textComponent.alignment = TMPro.TextAlignmentOptions.Left;

                // 添加相似度阈值高亮
                if (similarity > 0.8f) // 高相似度阈值
                {
                    textComponent.color = Color.yellow;
                    textComponent.fontStyle = TMPro.FontStyles.Bold;
                }
                else if (isHighlighted)
                {
                    textComponent.color = Color.green;
                }

                LogKit.I($"[{GetType().FullName}::CreateRecognitionDisplayItem] Created item for {templateName}: {similarity:F3}");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::CreateRecognitionDisplayItem] Error creating item for {templateName}: {ex.Message}");
            }
        }

        // 旧的UpdateButtonStates方法已删除，使用UIStateManager管理按钮状态

        private void OnBattlePlayerMovement(BattlePlayerMovementEvent e)
        {
            if (_isRecording && e?.MovementData != null)
            {
                _recordedMovements.Add(e.MovementData);
            }
        }

        private void StartRecording()
        {
            _isRecording = true;
            _recordedMovements.Clear();
            LogKit.I($"[{GetType().FullName}::StartRecording] Started recording gesture: {_currentGestureName}");
        }

        private void StopRecording()
        {
            _isRecording = false;
            LogKit.I($"[{GetType().FullName}::StopRecording] Stopped recording, collected {_recordedMovements.Count} data points");
        }

        /// <summary>
        /// 保存录制数据到JSON文件
        /// </summary>
        private void SaveRecordingToJson()
        {
            try
            {
                if (_recordedMovements.Count == 0)
                {
                    LogKit.W($"[{GetType().FullName}::SaveRecordingToJson] No data to save");
                    return;
                }

                // 转换为TimestampedMovementData
                _recordedSequence.Clear();
                var baseTime = System.DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

                for (int i = 0; i < _recordedMovements.Count; i++)
                {
                    _recordedSequence.Add(new TimestampedMovementData
                    {
                        TimestampMs = baseTime + (i * 20), // 假设50Hz采样率
                        MovementData = _recordedMovements[i]
                    });
                }

                // 弹出保存对话框
                var savePath = EditorUtility.SaveFilePanel("保存录制数据", "", "recording", "json");
                if (string.IsNullOrEmpty(savePath))
                {
                    return;
                }

                // 序列化并保存
                var json = JsonUtility.ToJson(new RecordingDataWrapper { data = _recordedSequence }, true);
                System.IO.File.WriteAllText(savePath, json);

                LogKit.I($"[{GetType().FullName}::SaveRecordingToJson] Recording saved to: {savePath}");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::SaveRecordingToJson] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 录制数据包装器
        /// </summary>
        [System.Serializable]
        private class RecordingDataWrapper
        {
            public List<TimestampedMovementData> data;
        }

        // 简化的扩展功能实现

        private void OnHeadOrientationToggleChanged(bool value)
        {
            // 头部朝向配置变更
            LogKit.I($"[{GetType().FullName}::OnHeadOrientationToggleChanged] Head orientation: {value}");
        }

        // 事件处理方法
        private void OnGestureRecognized(GestureRecognizedEvent e)
        {
            _recognitionUIManager?.StartDisplayTimer(e.GestureName, e.Similarity);

            // 触发显示更新
            if (_uiStateManager.CurrentState == UIStateManager.UIState.Recognition)
            {
                UpdateRecognitionDisplay();
            }
        }

        private void OnGestureRecognitionUpdate(GestureRecognitionUpdateEvent e)
        {
            _recognitionUIManager?.UpdateCurrentSimilarity(e.GestureName, e.Similarity);

            // 触发显示更新（使用计时器机制避免频繁更新）
            if (_uiStateManager.CurrentState == UIStateManager.UIState.Recognition)
            {
                // 每隔一定时间更新一次显示，避免性能问题
                if (Time.time - _lastDisplayUpdateTime > 0.1f) // 100ms更新间隔
                {
                    UpdateRecognitionDisplay();
                    _lastDisplayUpdateTime = Time.time;
                }
            }
        }

        private void OnGestureRecognitionStarted(GestureRecognitionStartedEvent e)
        {
            LogKit.I($"[{GetType().FullName}::OnGestureRecognitionStarted] Recognition started");

            // 初始化显示
            if (_uiStateManager.CurrentState == UIStateManager.UIState.Recognition)
            {
                UpdateRecognitionDisplay();
            }
        }

        private void OnGestureRecognitionStopped(GestureRecognitionStoppedEvent e)
        {
            LogKit.I($"[{GetType().FullName}::OnGestureRecognitionStopped] Recognition stopped");

            // 清理显示
            ClearRecognitionDisplayItems();
            if (poseTemplateList != null)
            {
                poseTemplateList.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// 统一的文件选择取消处理
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="targetState">目标状态</param>
        private void HandleFileSelectionCancelled(string operation, UIStateManager.UIState targetState)
        {
            try
            {
                LogKit.I($"[{GetType().FullName}::HandleFileSelectionCancelled] 用户取消{operation}操作");
                _uiStateManager.SetState(targetState);
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::HandleFileSelectionCancelled] Error handling cancellation: {ex.Message}");
                // 确保在异常情况下也能回退到安全状态
                _uiStateManager.SetState(UIStateManager.UIState.Default);
            }
        }

        /// <summary>
        /// 统一的文件操作错误处理
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="error">错误信息</param>
        /// <param name="fallbackState">回退状态</param>
        private void HandleFileOperationError(string operation, string filePath, string error, UIStateManager.UIState fallbackState)
        {
            try
            {
                LogKit.E($"[{GetType().FullName}::HandleFileOperationError] {operation}操作失败: {filePath}, Error: {error}");
                _uiStateManager.SetState(fallbackState);
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::HandleFileOperationError] Error handling file operation error: {ex.Message}");
                // 确保在异常情况下也能回退到安全状态
                _uiStateManager.SetState(UIStateManager.UIState.Default);
            }
        }
    }
}
