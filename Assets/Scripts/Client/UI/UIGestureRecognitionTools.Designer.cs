using System;
using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TMPro;

namespace TFG.UI
{
	// Generate Id:a1b2c3d4-5e6f-7890-abcd-ef1234117890
	public partial class UIGestureRecognitionTools
	{
		public const string Name = "UIGestureRecognitionTools";

		[SerializeField] public ScrollRect poseTemplateList;
		[SerializeField] public TextMeshProUGUI textTemplate;

		[SerializeField] public Button startRecordingButton;
		[SerializeField] public Button playbackButton;
		[SerializeField] public Button recognitionButton;
		[SerializeField] public Button saveTemplateButton;
		[SerializeField] public Button btnExit;

		[SerializeField] public RectTransform jointSelectionContainer;
		[SerializeField] public Toggle headOrientationToggle;

		private UIGestureRecognitionToolsData mPrivateData = null;

		protected override void ClearUIComponents()
		{
			poseTemplateList = null;
			textTemplate = null;

			startRecordingButton = null;
			playbackButton = null;
			recognitionButton = null;
			saveTemplateButton = null;
			btnExit = null;

			jointSelectionContainer = null;
			headOrientationToggle = null;

			mData = null;
		}

		public UIGestureRecognitionToolsData Data
		{
			get { return mData; }
		}

		UIGestureRecognitionToolsData mData
		{
			get { return mPrivateData ?? (mPrivateData = new UIGestureRecognitionToolsData()); }
			set
			{
				mUIData = value;
				mPrivateData = value;
			}
		}
	}
}
