using System;
using Client.Model;
using Client.Model.Character;
using ClientDeviceManager;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.Command;
using GameClient.Model;
using GameClient.XR_Demo;
using QFramework;
using QFramework.PrefsKit;
using UnityEngine;
using XrReference;
using IController = QFramework.IController;

namespace TFG.UI
{
	public class UIHeightCalibrationData : UIPanelData
	{
	}
	public partial class UIHeightCalibration : UIPanel, IController
	{
		private float _height;
		private bool _leftTriggerDown = false;
		private float _startLeftTime;
		private bool _rightTriggerDown = false;
		private float _startRightTime;
		private float _lastTriggerTime;

		private VRNodeRightController _rightController;

		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as UIHeightCalibrationData ?? new UIHeightCalibrationData();
		}

		protected override void OnOpen(IUIData uiData = null)
		{
			InputEvent.Instance.onLeftTriggerEnter += () =>
			{
				_leftTriggerDown = true;
				_startLeftTime = Time.time;
			};


			InputEvent.Instance.onLeftTriggerUp += () =>
			{
				_leftTriggerDown = false;
			};

			InputEvent.Instance.onRightTriggerEnter += () =>
			{
				_rightTriggerDown = true;
				_startRightTime = Time.time;
			};


			InputEvent.Instance.onRightTriggerUp += () =>
			{
				_rightTriggerDown = false;
			};
			
			SuccessBtn.OnClickEvent += SuccessBtnOnOnClickEvent;
			ActionKit.OnUpdate.Register(CheckDualTriggerHold); 
		}

		private void SuccessBtnOnOnClickEvent()
		{
			this.SendCommand(new GoToMainMenuCommand());
		}

		protected override void OnShow()
		{
		}

		protected override void OnHide()
		{
		}

		protected override void OnClose()
		{
			ActionKit.OnUpdate.UnRegister(CheckDualTriggerHold);
			SuccessBtn.OnClickEvent -= SuccessBtnOnOnClickEvent;
		}

		void CheckDualTriggerHold()
		{
			// 防抖，2s 调用一次身高校准
			if (Time.time - _lastTriggerTime <= 2f)
				return;

			if ((Time.time - _startLeftTime) > 2f && (Time.time - _startRightTime) > 2f && _leftTriggerDown && _rightTriggerDown)
			{
				float height = DeviceManager.Instance.GetVRNodesTransform(VRDeviceNodeType.XRMainCam).transform.position.y;
				_lastTriggerTime = Time.time;
				XRWrapper.Instance.CurrentDevice.CalibrateSkeleton(height);
				Value.text = height.ToString();

				var playerId = this.GetModel<LocalPlayerModel>().SelfPlayerID;  
                var model = LaunchMainArch.Interface.GetModel<CharacterManagerModel>();
                if (model.CharacterDic.ContainsKey(playerId))
                {
                    return;
                }

                LaunchMainArch.Interface.GetSystem<CharacterManagerSystem>().CreateCharacter(playerId, null, true).Forget();
                this.SendCommand(new GoToMainMenuCommand());
            }
		}

        private void Update()
        {
            bool isShiftHeld = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
            bool isAlpha2Pressed = Input.GetKeyDown(KeyCode.Alpha2);
            if (isShiftHeld && isAlpha2Pressed)
            {
                UIKit.CloseAllPanel();
                UIKit.OpenPanel<UILoginPanel>().Forget();
            }
        }

        public IArchitecture GetArchitecture()
		{
			return LaunchMainArch.Interface;
		}
	}
}
