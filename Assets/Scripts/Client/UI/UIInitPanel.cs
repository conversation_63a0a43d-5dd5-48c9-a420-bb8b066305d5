using System;
using System.Collections.Generic;
using System.IO;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.Event;
using GameClient.Event.ClientNetEvent;
using GameClient.System;
using GameMain.Scripts;
using GameShare.Utility;
using LiteNetLib;
using QFramework;
using TFGShare.Protocol;
using static TFGShare.Protocol.BroadcastServerInfo;
using IController = QFramework.IController;
using Client.Event.InputEvents;
using Client.System.Battle;
using Client.System.Camera;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFG.UI
{
    public class UIInitPanelData : UIPanelData
    {
    }
    
    public partial class UIInitPanel : UIPanel, IController, ICanSendEvent
    {
        private readonly Dictionary<BroadcastServerInfo, UIServerNode> _dictNodes = new(new EqualityComparer());
        private readonly List<BroadcastServerInfo> _cachedServerInfo = new();
        private BroadcastServerInfo _selectedServerInfo = null;

        private readonly UnRegisterList unRegisterHelper = new();
        private void EnableBtns(bool enable)
        {
            if (CreateServer != null)
            {
                CreateServer.gameObject.SetActive(enable);
            }

            if (FindServer != null)
            {
                FindServer.gameObject.SetActive(enable);
            }

            if (Connect != null)
            {
                Connect.gameObject.SetActive(enable);
            }

            if (ConnectServer != null)
            {
                ConnectServer.gameObject.SetActive(enable);
            }
        }

        protected override void OnInit(IUIData uiData = null)
        {
            mData = uiData as UIInitPanelData ?? new UIInitPanelData();
            UIServerNode.Hide();

            ClearServerNodes();

            CreateServer.gameObject.SetActive(false);
            ConnectServer.gameObject.SetActive(false);

            FindServer.onClick.RemoveAllListeners();
            FindServer.onClick.AddListener(async () =>
            {
                EnableBtns(false);
                ClearServerNodes();
                this.GetUtility<DiscoveryUtility>()?.Init(null, ShowDiscovery);
                await UniTask.Delay(15000);
                this.GetUtility<DiscoveryUtility>()?.Stop();
                EnableBtns(true);
            });

            Connect.onClick.RemoveAllListeners();
            Connect.onClick.AddListener(() =>
            {
                EnableBtns(false);
                _selectedServerInfo = new BroadcastServerInfo
                {
                    Name = "直连服务器",
                    IP = GameStartupConfig.Instance.ServerHost,
                    Port = GameStartupConfig.Instance.ServerPort,
                    LastUpdate = DateTime.Now,
                    ServerType = (int)GameType.Server
                };

                EnableBtns(true);
                ConnectServer.onClick.Invoke();
            });

            CreateServer.onClick.RemoveAllListeners();
            CreateServer.onClick.AddListener(() =>
            {
                EnableBtns(false);
                this.SendCommand(new RunHotfixCommand()
                {
                    RunDllName = "GameServer.dll",
                    TypeName = "GameServer"
                });
                CreateServer.gameObject.SetActive(false);
                EnableBtns(true);
                FindServer.onClick?.Invoke();
            });

            ConnectServer.onClick.RemoveAllListeners();
            ConnectServer.onClick.AddListener(async () =>
            {
                if (_selectedServerInfo == null)
                {
                    return;
                }

                EnableBtns(false);
                //添加连接的服务器是否是自己创建的服务器
                bool isCreatedBySelf = NetUtils.GetLocalIp(LocalAddrType.IPv4) == _selectedServerInfo.IP;
                this.GetSystem<RoomSystem>().ConnectToServer(_selectedServerInfo.IP, _selectedServerInfo.Port);
                ConnectServer.gameObject.SetActive(false);
                EnableBtns(true);
                //调整时序，尽量保证事件发送时连接是成功的
                this.SendEvent(new ClientConnectServerSuccessEvent(_selectedServerInfo, isCreatedBySelf));
                CloseSelf();
                
                await GetArchitecture().GetSystem<EnvScenesSystem>().LoadDefaultScene();
                await UIKit.OpenPanel<UILoginPanel>();
            });

            if (GameConfig.CurrentGameType == GameType.PlayerServer || GameConfig.CurrentGameType == GameType.GMServer)
            {
                FindServer.onClick?.Invoke();
            }

            this.RegisterEvent<InputActionStartedEvent>(ShowPoseEditorScene);
        }

        private void ShowPoseEditorScene(InputActionStartedEvent e)
        {
            if (e.ActionName != "ShowPoseEditor")
            {
                return;
            }

            UIKit.CloseAllPanel();
            UIKit.OpenPanel<UIPoseEditorPanel>().Forget();
        }

        private bool CanCreateServer()
        {
            if (GameConfig.CurrentGameType != GameType.Player && GameConfig.CurrentGameType != GameType.GM)
            {
                return false;
            }

            var localHost = NetUtils.GetLocalIp(LocalAddrType.IPv4);
            foreach (var serverInfo in _cachedServerInfo)
            {
                if (serverInfo.IP == localHost)
                {
                    return false;
                }
            }

            return true;
        }

        private bool CanConnectToServer()
        {
            if (_selectedServerInfo == null)
            {
                return false;
            }

            var curType = GameConfig.CurrentGameType;
            var localHost = NetUtils.GetLocalIp(LocalAddrType.IPv4);
            if (curType == GameType.Player || curType == GameType.PlayerServer)
            {
                foreach (var serverInfo in _cachedServerInfo)
                {
                    if (serverInfo.IP == localHost)
                    {
                        return _selectedServerInfo == serverInfo;
                    }
                }

                return true;
            }
            else if (curType == GameType.GM && (_selectedServerInfo.IP == localHost || _selectedServerInfo.ServerType == (int)GameType.Server))
            {
                return true;
            }
            else if ((curType == GameType.GMServer || curType == GameType.PlayerServer) && localHost == _selectedServerInfo.IP)
            {
                return true;
            }
            else if (curType == GameType.Guest)
            {
                return true;
            }

            return false;
        }

        private void ClearServerNodes()
        {
            foreach (var node in _dictNodes)
            {
                node.Value.Hide();
                Destroy(node.Value.gameObject);
            }
            _dictNodes.Clear();
            _cachedServerInfo.Clear();
        }

        protected override void OnOpen(IUIData uiData = null)
        {
            this.RegisterEvent<InputActionStartedEvent>(CommonTest);
        } 
        

        private void CommonTest(InputActionStartedEvent e)
        {
            if (e.ActionName == "LoadSettings")
            {
                UIKit.OpenPanel<UISettings>().Forget();
            } else if (e.ActionName == "CameraShake")
            {
                var system = this.GetSystem<CameraSystem>(); 
                system.TriggerConfigShake("Shake_Test1");
            }
        }

        private void ShowDiscovery(List<BroadcastServerInfo> ret)
        {
            if (ret == null || ret.Count == 0)
            {
                return;
            }

            ret.Sort((x, y) =>
            {
                var xx = $"{x.Name}-{x.IP}-{x.Port}";
                var yy = $"{y.Name}-{y.IP}-{y.Port}";
                return xx.CompareTo(yy);
            });
            var same = ret.Count == _cachedServerInfo.Count;
            if (same)
            {
                for (var i = 0; i < ret.Count; i++)
                {
                    if (_dictNodes.TryGetValue(ret[i], out var node) && node != null)
                    {
                        node.UpdateDate(ret[i].LastUpdate);
                    }

                    if (!ret[i].Equals(_cachedServerInfo[i]))
                    {
                        same = false;
                        break;
                    }
                }
            }

            if (same)
            {
                return;
            }

            ClearServerNodes();
            foreach (var bsi in ret)
            {
                _cachedServerInfo.Add(bsi);
                var serverNode = Instantiate(UIServerNode, Server);
                serverNode.Show();
                serverNode.UpdateInfo(bsi, GameConfig.CurrentGameType);
                _dictNodes[bsi] = serverNode;
            }

            this.GetUtility<DiscoveryUtility>()?.Stop();
        }

        private void DoCreateServerEvent(CreateServerEvent evt)
        {
            CreateServer.gameObject.SetActive(evt.Visible);
        }

        private void DoConnectServerEvent(ConnectServerEvent evt)
        {
            ConnectServer.gameObject.SetActive(evt.Visible);
        }

        private void OnClickBroadcastServerEvent(ClickBroadcastServerEvent evt)
        {
            this.SendEvent(new CreateServerEvent()
            {
                Visible = false
            });

            this.SendEvent(new ConnectServerEvent()
            {
                Visible = false
            });

            if (evt.Server == null)
            {
                if (_selectedServerInfo != null)
                {
                    LogKit.I($"[UIInitPanel::OnClickBroadcastServerEvent] 解除选择");
                }
                _selectedServerInfo = null;

                if (CanCreateServer())
                {
                    this.SendEvent(new CreateServerEvent()
                    {
                        Visible = true
                    });
                }
                return;
            }

            if (_selectedServerInfo == evt.Server)
            {
                return;
            }

            _selectedServerInfo = evt.Server;

            if (CanConnectToServer())
            {
                this.SendEvent(new ConnectServerEvent()
                {
                    Visible = true,
                });
            }

            LogKit.I($"[UIInitPanel::OnClickBroadcastServerEvent] 选择了{_selectedServerInfo}");
            this.SendEvent(new ClickBroadcastServerNodeEvent()
            {
                Server = evt.Server,
            });
        }

        protected override void OnShow()
        {
            this.RegisterEvent<CreateServerEvent>(DoCreateServerEvent).AddToUnregisterList(unRegisterHelper);
            this.RegisterEvent<ConnectServerEvent>(DoConnectServerEvent).AddToUnregisterList(unRegisterHelper);
            this.RegisterEvent<ClickBroadcastServerEvent>(OnClickBroadcastServerEvent).AddToUnregisterList(unRegisterHelper);

            OnClickBroadcastServerEvent(new ClickBroadcastServerEvent()
            {
                Server = null
            });
        }

        protected override void OnHide()
        {
            unRegisterHelper.UnRegisterAll();
        }

        protected override void OnClose()
        {
        }

        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
    }
}
