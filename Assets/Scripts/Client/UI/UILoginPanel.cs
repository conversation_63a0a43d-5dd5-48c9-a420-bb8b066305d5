using System;
using Client.Model;
using Client.System.SettingSystem;
using Client.UI.Login.Reset;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;
using TMPro;
using UnityEngine;

namespace TFG.UI
{
    /// <summary>
    /// 登录面板数据
    /// </summary>
    public class UILoginPanelData : UIPanelData
    {
    }

    /// <summary>
    /// 登录面板 - 处理登录UI交互
    /// </summary>
    public partial class UILoginPanel : UIPanel, IController, ICanSendEvent
    {
        public ClientAccountPreferencesModel PreferencesModel =>
            this.GetModel<ClientAccountPreferencesModel>();

        [HideInInspector] public UIFSMManager UIfsmManager = new UIFSMManager();
        [Header("UI 状态机")] public StateLanding StateLanding;

        /*登录*/
        public StateLoginLayerState StateLoginLayerState;
        public StateLoginEntry StateLoginEntry;
        public StateLoginWithEmailPassword StateLoginWithEmailPassword;
        public StateLoginWithPhoneVerification StateLoginWithPhoneVerification;
        public StateLoginWithPhonePassword StateLoginWithPhonePassword;

        /*注册*/
        public StateRegisterLayerState StateRegisterLayerState;
        public StateRegisterEntry StateRegisterEntry;
        public StateRegisterVerificationCodeWithEmail StateRegisterVerificationCodeWithEmail;
        public StateRegisterVerificationCodeWithPhone StateRegisterVerificationCodeWithPhone;
        public StateRegisterEmailSettingPassword StateRegisterEmailSettingPassword;
        public StateRegisterPhoneSettingPassword StateRegisterPhoneSettingPassword;
        public StateRegisterSuccess StateRegisterSuccess;
        public StateRegisterSetPasswordNow StateRegisterSetPasswordNow;

        /*重置密码*/
        public StateResetLayerState StateResetLayerState;
        public StateResetEntry StateResetEntry;
        public StateResetVerificationCodeWithEmail StateResetVerificationCodeWithEmail;
        public StateResetVerificationCodeWithPhone StateResetVerificationCodeWithPhone;
        public StateResetEmailSettingPassword StateResetEmailSettingPassword;
        public StateResetPhoneSettingPassword StateResetPhoneSettingPassword;
        public StateResetSuccess StateResetSuccess;
        [Header("主交互区根CanvasGroup")] public CanvasGroup ContentCanvasGroup;
        [Header("通用组件")] public UIAccountTypeTagComponent UIAccountTypeTagComponent;
        public UIBackToLastStateButtonComponent UIBackToLastStateButtonComponent;
        public UIAutoLoginToggleComponent UIAutoLoginToggleComponent;
        public UILoginComponent UILoginComponent;
        public UINextComponent UINextComponent;
        public UIConfirmAndCancelComponent UIConfirmAndCancelComponent;
        public UILabelComponent UILabelComponent;
        public UITitleLabelComponent UITitleComponent;
        public UIBackToLoginComponent UIBackToLoginComponent;
        [Header("邮箱组件")] public UIEmailInputComponent UIEmailInputComponent;
        [Header("手机")] public UIPhoneNumberInputComponent UIPhoneNumberInputComponent;
        [Header("密码")] public UILoginPasswordInputComponent UILoginPasswordInputComponent;
        public UIChangeToResetPasswordComponent UIChangeToResetPasswordComponent;
        public UIChangeVerificationCheckTypeComponent UIChangeVerificationCheckTypeComponent;
        public UISetPasswordComponent UISetPasswordComponent;
        [Header("验证码")] public UISendVerificationRequestComponent UISendVerificationRequestComponent;
        public UIVerificationCodeInputComponent UIVerificationCodeInputComponent;
        public UIChangePasswordCheckTypeComponent UIChangePasswordCheckTypeComponent;

        public TMP_Text VersionTxt;
        
        public LocalizationSystem LocalizationSystem;
        //登录成功保留TmpEmail
        //验证码请求成功保留TmpEmail
        //注册成功保留TmpEmail
        //重置密码成功保留TmpEmail
        [HideInInspector] public string TmpEmail { get; set; } = "";
        [HideInInspector] public string TmpPhone { get; set; } = "";
        [HideInInspector] public string TmpCountryCode { get; set; } = "";
        [HideInInspector] public int TmpCountryCodeIndex { get; set; } = 0;
        [HideInInspector] public string TmpPassword { get; set; } = "";
        [HideInInspector] public string TmpAccount => string.Concat("+",TmpCountryCode, "-", TmpPhone);

        /// <summary>
        /// 初始化面板，注册事件和按钮回调
        /// </summary>
        protected override void OnInit(IUIData uiData = null)
        {
            mData = uiData as UILoginPanelData ?? new UILoginPanelData();
            LocalizationSystem = this.GetSystem<LocalizationSystem>();
        }

        /// <summary>
        /// 打开面板时重置UI状态
        /// </summary>
        protected override void OnOpen(IUIData uiData = null)
        {
            if (LocalizationSystem == null)
            {
                LocalizationSystem = this.GetSystem<LocalizationSystem>();
            }
            /*Login*/
            StateLanding.BindPanel(this);
            StateLoginLayerState.BindPanel(this);
            StateLoginEntry.BindPanel(this);
            StateLoginWithEmailPassword.BindPanel(this);
            StateLoginWithPhoneVerification.BindPanel(this);
            StateLoginWithPhonePassword.BindPanel(this);
            /*Register*/
            StateRegisterLayerState.BindPanel(this);
            StateRegisterEntry.BindPanel(this);
            StateRegisterVerificationCodeWithEmail.BindPanel(this);
            StateRegisterVerificationCodeWithPhone.BindPanel(this);
            StateRegisterEmailSettingPassword.BindPanel(this);
            StateRegisterPhoneSettingPassword.BindPanel(this);
            StateRegisterSuccess.BindPanel(this);
            StateRegisterSetPasswordNow.BindPanel(this);
            /*Reset*/
            StateResetLayerState.BindPanel(this);
            StateResetEntry.BindPanel(this);
            StateResetEmailSettingPassword.BindPanel(this);
            StateResetPhoneSettingPassword.BindPanel(this);
            StateResetSuccess.BindPanel(this);
            StateResetVerificationCodeWithEmail.BindPanel(this);
            StateResetVerificationCodeWithPhone.BindPanel(this);
            var ver = LocalizationSystem.GetLocalizationContent("Version");
            var verLabel = string.Concat(ver, GetArchitecture().GetModel<ClientApplicationModel>().AppVersion);
            VersionTxt.SetText(verLabel);
            
            UIfsmManager.ChangeState(StateLanding).Forget();
        }

        /// <summary>
        /// 关闭面板时清理事件监听
        /// </summary>
        protected override void OnClose()
        {
            UIfsmManager.ChangeState(null).Forget();
            TmpEmail = "";
            TmpPhone = "";
            TmpCountryCode = "";
            TmpCountryCodeIndex = 0;
            this.GetModel<ClientAccountPreferencesModel>().CacheVerifyCode = "";

        }

        private void Update()
        {
            UIfsmManager?.CurrentState?.OnUpdate();
            
            // GMDebug使用，Release请删除
            if (Input.GetKeyDown(KeyCode.Z))
            {
                this.SendCommand(new GMFastLoginCommand());
            }
            bool isShiftHeld = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
            bool isAlpha1Pressed = Input.GetKeyDown(KeyCode.Alpha1);
            if (isShiftHeld && isAlpha1Pressed)
            {
                if (this.GetModel<ClientAccountPreferencesModel>().IsAutoLogin)
                {
                    this.GetModel<ClientAccountPreferencesModel>().IsAutoLogin = false;
                }
                else
                {
                    this.GetModel<ClientAccountPreferencesModel>().IsAutoLogin = true;
                }
            }
            
        }

        /// <summary>
        /// 校验邮箱输入，并在失败时弹窗提示。
        /// </summary>
        /// <param name="buttonToReset">校验失败后需要恢复的按钮</param>
        /// <returns>true代表校验通过</returns>
        public async UniTask<bool> ValidateEmailAsync(UISmartButton buttonToReset)
        {
            var validationResult = UIEmailInputComponent.ForceValidate();
            if (validationResult == EmailValidationResult.Valid)
            {
                return true;
            }

            string message = UIEmailInputComponent.GetMessageForResult(validationResult);
            string error = LocalizationSystem.GetLocalizationContent("Error");
            string confrim = LocalizationSystem.GetLocalizationContent("OK");
            await UIDialogHelper.ShowNormalStyle(error, message, true, false, confirmText:confrim);
            buttonToReset.EnableBtn();
            return false;
        }

        /// <summary>
        /// 校验登录密码，并在失败时弹窗提示。
        /// </summary>
        public async UniTask<bool> ValidateLoginPasswordAsync(UISmartButton buttonToReset)
        {
            var validationResult = UILoginPasswordInputComponent.ForceValidation();
            if (validationResult == LoginPasswordValidationResult.Valid)
            {
                return true;
            }

            string message = UILoginPasswordInputComponent.GetMessageForResult(validationResult);
            string error = LocalizationSystem.GetLocalizationContent("Error");
            string confrim = LocalizationSystem.GetLocalizationContent("OK");
            await UIDialogHelper.ShowNormalStyle(error, message, true, false, confirmText:confrim);
            buttonToReset.EnableBtn();
            return false;
        }

        /// <summary>
        /// 校验手机号输入，并在失败时弹窗提示。
        /// </summary>
        public async UniTask<bool> ValidatePhoneNumberAsync(UISmartButton buttonToReset)
        {
            var validationResult = UIPhoneNumberInputComponent.ForceValidate();
            if (validationResult == PhoneNumberValidationResult.Valid)
            {
                return true;
            }

            string message = UIPhoneNumberInputComponent.GetMessageForResult(validationResult);
            string error = LocalizationSystem.GetLocalizationContent("Error");
            string confrim = LocalizationSystem.GetLocalizationContent("OK");
            await UIDialogHelper.ShowNormalStyle(error, message, true, false, confirmText:confrim);
            buttonToReset.EnableBtn();
            return false;
        }

        /// <summary>
        /// 校验验证码输入，并在失败时弹窗提示。
        /// </summary>
        /// <param name="buttonToReset">校验失败后需要恢复的按钮</param>
        /// <returns>true代表校验通过</returns>
        public async UniTask<bool> ValidateVerificationCodeAsync(UISmartButton buttonToReset)
        {
            var validationResult = UIVerificationCodeInputComponent.ForceValidation();
            if (validationResult == VerificationCodeValidationResult.Valid)
            {
                return true;
            }

            string message = UIVerificationCodeInputComponent.GetMessageForResult(validationResult);
            string error = LocalizationSystem.GetLocalizationContent("Error");
            string confrim = LocalizationSystem.GetLocalizationContent("OK");
            await UIDialogHelper.ShowNormalStyle(error, message, true, false, confirmText:confrim);
            buttonToReset.EnableBtn();
            return false;
        }

        /// <summary>
        /// 校验设置的密码强度，并在失败时弹窗提示。
        /// </summary>
        /// <remarks>
        /// 此方法将 Valid_Strong 和 Valid_Medium 均视为有效密码。
        /// </remarks>
        /// <param name="buttonToReset">校验失败后需要恢复的按钮</param>
        /// <returns>true代表校验通过</returns>
        public async UniTask<bool> ValidateSetPasswordAsync(UISmartButton buttonToReset)
        {
            var validationResult = UISetPasswordComponent.ForceValidation();

            // 检查是否为两种有效的密码强度之一
            if (validationResult == SetPasswordValidationResult.Valid_Strong ||
                validationResult == SetPasswordValidationResult.Valid_Medium)
            {
                return true;
            }

            // 如果校验失败，则获取消息并显示
            string message = UISetPasswordComponent.GetMessageForResult(validationResult);
            string error = LocalizationSystem.GetLocalizationContent("Error");
            string confrim = LocalizationSystem.GetLocalizationContent("OK");
            await UIDialogHelper.ShowNormalStyle(error, message, true, false, confirmText:confrim);
            buttonToReset.EnableBtn();
            return false;
        }

        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
    }
}
