using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using QFramework;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace TFG.UI
{
    // 定义密码强度等级
    public enum PasswordStrength
    {
        None, // 不满足基本要求（如长度、非法字符）
        Weak,
        Medium,
        Strong
    }

    public enum SetPasswordValidationResult
    {
        Valid_Strong,
        Valid_Medium,
        Error_Weak,
        Error_IsEmpty,
        Error_TooShort,
        Error_ForbiddenCharacter
    }

    public class OnSetPasswordValidationChanged
    {
        public SetPasswordValidationResult Result;
        public PasswordStrength Strength;

        public bool IsValid =>
            Result == SetPasswordValidationResult.Valid_Medium ||
            Result == SetPasswordValidationResult.Valid_Strong;
    }

    [Serializable]
    public partial class UISetPasswordComponent : UIComponent, ICanSendEvent, ICanRegisterEvent
    {
        [Header("UI组件引用 (UI References)")] public CanvasGroup CanvasGroup;
        public LayoutElement LayoutElement;
        public TMP_InputField PasswordInputField;
        public TMP_Text PasswordLabel;
        public TMP_Text ConfirmPasswordLabel;
        public List<CanvasGroup> UITips;
        public string Password => PasswordInputField?.text;

        // 校验规则常量
        private const int MinCharacterCount = 8;
        private const int MaxCharacterCount = 24;

        // 用于强度检测的正则表达式
        private static readonly Regex HasLetters = new Regex("[a-zA-Z]");
        private static readonly Regex HasNumbers = new Regex("[0-9]");
        private static readonly Regex HasSpecialChars = new Regex(@"[^a-zA-Z0-9]");

        // 组件内部状态
        public bool IsValid { get; private set; } = false;
        public PasswordStrength CurrentStrength { get; private set; } = PasswordStrength.None;
        private SetPasswordValidationResult _currentResult;
        private float _originalCaretWidth;
        private LocalizationSystem LocalizationSystem;

        public async UniTask InitAndShow()
        {
            await UniTask.Yield();
            LocalizationSystem = this.GetArchitecture().GetSystem<LocalizationSystem>();
            LayoutElement.ignoreLayout = false;
            PasswordInputField.onValueChanged.AddListener(OnInputFieldValueChanged);
            PasswordInputField.onSelect.AddListener(ShowLogcat);
            PasswordInputField.onDeselect.AddListener(HideLogcat);
            PasswordInputField.text = String.Empty;
            _originalCaretWidth = PasswordInputField.caretWidth;
            OnInputFieldValueChanged(String.Empty);
            UpdateValidationState(SetPasswordValidationResult.Error_IsEmpty);
            CanvasGroup.EnableView();
        }

        public void HideAndDeinit()
        {
            Clear();
            CanvasGroup.DisableView();
            LayoutElement.ignoreLayout = true;
        }

        private void Clear()
        {
            PasswordInputField.onValueChanged.RemoveListener(OnInputFieldValueChanged);
            PasswordInputField.onSelect.RemoveListener(ShowLogcat);
            PasswordInputField.onDeselect.RemoveListener(HideLogcat);
            PasswordInputField.text = string.Empty;
        }

        public void ResetInput()
        {
            PasswordLabel.GetComponent<CanvasGroup>().alpha = 0;
            ConfirmPasswordLabel.GetComponent<CanvasGroup>().alpha = 1;
            PasswordInputField.text = string.Empty;
            OnInputFieldValueChanged(string.Empty);
            UpdateValidationState(SetPasswordValidationResult.Error_IsEmpty);
        }

        public void InitLabel()
        {
            PasswordLabel.GetComponent<CanvasGroup>().alpha = 1;
            ConfirmPasswordLabel.GetComponent<CanvasGroup>().alpha = 0;
        }

        protected override void OnDestroy() => Clear();

        public void ShowLogcat(string txt)
        {
            PasswordInputField.caretWidth = 1;
            PasswordInputField.MoveTextEnd(false);
        }
        public void HideLogcat(string txt)
        {
            PasswordInputField.caretWidth = (int)0f;
        }
        private void OnInputFieldValueChanged(string newText)
        {
            SetPasswordValidationResult validationResult = ForceValidation(newText);
            switch (validationResult)
            {
                case SetPasswordValidationResult.Valid_Strong:
                    foreach (var t in UITips)
                    {
                        t.DisableView();
                    }

                    UITips[2].EnableView();
                    break;
                case SetPasswordValidationResult.Valid_Medium:
                    foreach (var t in UITips)
                    {
                        t.DisableView();
                    }

                    UITips[1].EnableView();
                    break;
                case SetPasswordValidationResult.Error_Weak:
                case SetPasswordValidationResult.Error_IsEmpty:
                case SetPasswordValidationResult.Error_TooShort:
                case SetPasswordValidationResult.Error_ForbiddenCharacter:
                default:
                    foreach (var t in UITips)
                    {
                        t.DisableView();
                    }

                    UITips[0].EnableView();
                    break;
            }

            UpdateValidationState(validationResult);
        }

        public SetPasswordValidationResult ForceValidation(string password)
        {
            if (string.IsNullOrEmpty(password))
            {
                return SetPasswordValidationResult.Error_IsEmpty;
            }

            if (password.Contains("%") || password.Contains(" ") || password.Contains("\n") ||
                password.Contains("\r"))
            {
                return SetPasswordValidationResult.Error_ForbiddenCharacter;
            }

            if (password.Length < MinCharacterCount)
            {
                return SetPasswordValidationResult.Error_TooShort;
            }

            // 密码强度计算
            int score = 0;
            if (HasLetters.IsMatch(password)) score++;
            if (HasNumbers.IsMatch(password)) score++;
            if (HasSpecialChars.IsMatch(password)) score++;
            return score switch
            {
                1 => SetPasswordValidationResult.Error_Weak,
                2 => SetPasswordValidationResult.Valid_Medium,
                3 => SetPasswordValidationResult.Valid_Strong,
                _ => SetPasswordValidationResult.Error_Weak // 理论上不会执行到，作为兜底
            };
        }

        public SetPasswordValidationResult ForceValidation()
        {
            return ForceValidation(Password);
        }

        private void UpdateValidationState(SetPasswordValidationResult newResult, bool forceSendEvent = false)
        {
            if (newResult != _currentResult || forceSendEvent)
            {
                _currentResult = newResult;
                IsValid = (_currentResult == SetPasswordValidationResult.Valid_Medium ||
                           _currentResult == SetPasswordValidationResult.Valid_Strong);

                // 根据校验结果更新强度状态
                CurrentStrength = _currentResult switch
                {
                    SetPasswordValidationResult.Valid_Strong => PasswordStrength.Strong,
                    SetPasswordValidationResult.Valid_Medium => PasswordStrength.Medium,
                    SetPasswordValidationResult.Error_Weak => PasswordStrength.Weak,
                    _ => PasswordStrength.None
                };
                this.SendEvent(new OnSetPasswordValidationChanged
                {
                    Result = _currentResult, Strength = CurrentStrength
                });
            }
        }

        public string GetMessageForResult(SetPasswordValidationResult result)
        {
            switch (result)
            {
                case SetPasswordValidationResult.Valid_Strong:
                case SetPasswordValidationResult.Valid_Medium:
                    return string.Empty;
                case SetPasswordValidationResult.Error_Weak:
                    return LocalizationSystem.GetLocalizationContent("WeakPassword");
                case SetPasswordValidationResult.Error_IsEmpty:
                    return LocalizationSystem.GetLocalizationContent("EnterPassword");
                case SetPasswordValidationResult.Error_TooShort:
                    string baseStr = LocalizationSystem.GetLocalizationContent("TooShortPassword");
                    string finalMessage = string.Format(baseStr, MinCharacterCount);
                    return finalMessage;
                case SetPasswordValidationResult.Error_ForbiddenCharacter:
                    return LocalizationSystem.GetLocalizationContent("SpecialCharacters");
                default:
                    return LocalizationSystem.GetLocalizationContent("RequestError");
            }
        }

        public IArchitecture GetArchitecture() => LaunchMainArch.Interface;
    }
}
