using System;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using QFramework;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace TFG.UI
{
    // 定义登录密码的专属校验结果枚举
    public enum LoginPasswordValidationResult
    {
        Valid,
        Error_IsEmpty,
        E<PERSON>r_TooShort,
        E<PERSON>r_ForbiddenCharacter,
        Error_Backend_Incorrect
    }

    public class OnLoginPasswordValidationChanged
    {
        public LoginPasswordValidationResult Result;
        public bool IsValid => Result == LoginPasswordValidationResult.Valid;
    }

    [Serializable]
    public partial class UILoginPasswordInputComponent : UIComponent, ICanSendEvent, ICanRegisterEvent
    {
        [Header("UI组件引用 (UI References)")] public CanvasGroup CanvasGroup;
        public LayoutElement LayoutElement;
        public TMP_InputField PasswordInputField;
        public string Password => PasswordInputField?.text;

        // 校验规则常量
        private const int MinCharacterCount = 8;
        private const int MaxCharacterCount = 24;

        // 组件内部状态
        public bool IsValid { get; private set; } = false;
        private LoginPasswordValidationResult _currentResult;
        private LocalizationSystem LocalizationSystem;
        public async UniTask InitAndShow()
        {
            if (LocalizationSystem == null)
            {
                LocalizationSystem = this.GetArchitecture().GetSystem<LocalizationSystem>();
            }
            LayoutElement.ignoreLayout = false;
            await UniTask.Yield();
            PasswordInputField.text = String.Empty;
            PasswordInputField.onValueChanged.AddListener(OnInputFieldValueChanged);
            // 初始状态应为“为空”
            UpdateValidationState(LoginPasswordValidationResult.Error_IsEmpty, true);
            CanvasGroup.EnableView();
        }

        public void HideAndDeinit()
        {
            Clear();
            CanvasGroup.DisableView();
            LayoutElement.ignoreLayout = true;
        }


        private void Clear()
        {
            PasswordInputField.onValueChanged.RemoveListener(OnInputFieldValueChanged);
            PasswordInputField.text = string.Empty;
            UpdateValidationState(LoginPasswordValidationResult.Error_IsEmpty);
        }

        protected override void OnDestroy() => Clear();

        private void OnInputFieldValueChanged(string newText)
        {
            LoginPasswordValidationResult validationResult = ForceValidation(newText);
            UpdateValidationState(validationResult);
        }

        /// <summary>
        /// 校验逻辑现在返回一个详细的枚举结果
        /// </summary>
        public LoginPasswordValidationResult ForceValidation(string password)
        {
            // 规则1: 检查是否为空
            if (string.IsNullOrEmpty(password))
            {
                return LoginPasswordValidationResult.Error_IsEmpty;
            }

            // 规则2: 检查是否包含被禁止的字符
            if (password.Contains("%") || password.Contains(" ") || password.Contains("\n") ||
                password.Contains("\r"))
            {
                return LoginPasswordValidationResult.Error_ForbiddenCharacter;
            }

            // 规则3: 检查长度是否过短
            if (password.Length < MinCharacterCount)
            {
                return LoginPasswordValidationResult.Error_TooShort;
            }

            // 所有检查通过
            return LoginPasswordValidationResult.Valid;
        }

        public LoginPasswordValidationResult ForceValidation()
        {
            return ForceValidation(Password);
        }

        /// <summary>
        /// 更新状态并仅在结果变化时发送事件
        /// </summary>
        private void UpdateValidationState(LoginPasswordValidationResult newResult,
            bool forceSendEvent = false)
        {
            if (newResult != _currentResult || forceSendEvent)
            {
                _currentResult = newResult;
                IsValid = (_currentResult == LoginPasswordValidationResult.Valid);
                this.SendEvent(new OnLoginPasswordValidationChanged { Result = _currentResult });
            }
        }
        /// <summary>
        /// 辅助方法，将校验结果映射为具体的错误消息
        /// </summary>
        public string GetMessageForResult(LoginPasswordValidationResult result)
        {
            
            switch (result)
            {
                case LoginPasswordValidationResult.Valid:
                    return string.Empty; // 验证通过时，无消息
                case LoginPasswordValidationResult.Error_IsEmpty:
                    return LocalizationSystem.GetLocalizationContent("EnterPassword");
                case LoginPasswordValidationResult.Error_TooShort:
                    string baseStr = LocalizationSystem.GetLocalizationContent("TooShortPassword");
                    string finalMessage = string.Format(baseStr, MinCharacterCount);
                    return finalMessage;
                case LoginPasswordValidationResult.Error_ForbiddenCharacter:
                    return LocalizationSystem.GetLocalizationContent("SpecialCharacters");
                case LoginPasswordValidationResult.Error_Backend_Incorrect:
                    return LocalizationSystem.GetLocalizationContent("IncorrectPassword");
                default:
                    return LocalizationSystem.GetLocalizationContent("RequestError");
            }
        }

        public IArchitecture GetArchitecture() => LaunchMainArch.Interface;
    }
}
