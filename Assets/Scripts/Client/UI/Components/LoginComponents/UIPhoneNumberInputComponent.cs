using System;
using System.Linq;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using QFramework;
using TFG.UI.Common;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace TFG.UI
{
    public enum PhoneNumberValidationResult
    {
        Valid,
        Invalid_Nothing,
        Invalid_TooShort,
        Invalid_TooLong,
        Invalid_ContainsNonDigits,
        Invalid_DropdownNotSet,
        Invalid_NoOptionSelected
    }

    [Serializable]
    public partial class UIPhoneNumberInputComponent : UIComponent, ICanSendEvent, ICanRegisterEvent
    {
        public CanvasGroup CanvasGroup;
        public LayoutElement LayoutElement;
        public TMP_InputField UIPhoneNumberInputField;
        public TMP_Text UICountryCodeTMP;
        public UIDropdown UICountryCodeDropdown;
        public CanvasGroup UICountryCodeRoot;
        public string PhoneNumber => UIPhoneNumberInputField?.text;
        public string CountryCode => UICountryCodeDropdown.CurrentOption.additionalText;
        public int CountryCodeIndex => UICountryCodeDropdown.CurrentIndex;
        public string PhoneAccount => string.Concat("+",CountryCode + "-" + PhoneNumber);
        private float _originalCaretWidth;

        [SerializeField] private QFrameworkLocalizationProvider QFrameworkLocalizationProvider; //多语言
        
        // 核心校验逻辑
        // 规则1: 从当前选中的UIDropdown的对应数据中获取Minlimit和MaXlimit，只有输入的字符大于等于minlimit且小于等于maxlimit才通过验证
        // 规则2： 默认选中第0个dropdown选项
        public bool IsValid { get; private set; } = false;
        private LocalizationSystem LocalizationSystem;

        /// <summary>
        /// 初始化，外部设置默认值、并应用表现
        /// </summary>
        public async UniTask InitAndShow(string defaultPhoneNumber,UILoginPanel panel)
        {
            await UniTask.DelayFrame(3);
            LayoutElement.ignoreLayout = false;
            
            if (UICountryCodeDropdown != null)
            {
                UICountryCodeDropdown.SetLocalizationProvider(QFrameworkLocalizationProvider);
                UICountryCodeDropdown.OnSelectionChanged += OnCountryCodeChanged;
                var index = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex;
                UICountryCodeDropdown.Select(index);
                OnCountryCodeChanged(index, UICountryCodeDropdown.CurrentOption);
            }

            if (LocalizationSystem == null)
            {
                LocalizationSystem = this.GetArchitecture().GetSystem<LocalizationSystem>();
            }
            _originalCaretWidth = UIPhoneNumberInputField.caretWidth;
            UIPhoneNumberInputField.text = defaultPhoneNumber ?? string.Empty;
            UIPhoneNumberInputField.onValueChanged.AddListener(OnInputFieldValueChanged);
            UIPhoneNumberInputField.onSelect.AddListener(ShowLogcat);
            UIPhoneNumberInputField.onDeselect.AddListener(HideLogcat);
            
            OnInputFieldValueChanged(UIPhoneNumberInputField.text);
            CanvasGroup.EnableView();
            // 保证在OnEnable时，如果输入框默认不是选中状态，光标就是隐藏的
            if (!UIPhoneNumberInputField.isFocused)
            {
                HideLogcat(string.Empty);
            }
        }

        /// <summary>
        /// 初始化，外部重置表现、隐藏输入框
        /// </summary>
        public void HideAndDeinit()
        {
            Clear();
            CanvasGroup.DisableView();
            UICountryCodeDropdown.ClearAllEvents();
            LayoutElement.ignoreLayout = true;
        }

        
        public void ShowLogcat(string txt)
        {
            UIPhoneNumberInputField.caretWidth = 1;
            UIPhoneNumberInputField.MoveTextEnd(false);
        }
        public void HideLogcat(string txt)
        {
            UIPhoneNumberInputField.caretWidth = (int)0f;
        }
        private void Clear()
        {
            UIPhoneNumberInputField.onValueChanged.RemoveListener(OnInputFieldValueChanged);
            UICountryCodeDropdown.OnSelectionChanged -= OnCountryCodeChanged;
            UIPhoneNumberInputField.text = string.Empty;
            IsValid = false;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Clear();
        }

        /// <summary>
        /// 当国家代码下拉框选择变化时，重新校验当前输入的电话号码。
        /// </summary>
        private void OnCountryCodeChanged(int index, DropdownOption option)
        {
            OnInputFieldValueChanged(UIPhoneNumberInputField.text);
            UICountryCodeTMP.SetText(option.additionalText);
            this.GetArchitecture().GetModel<ClientAccountPreferencesModel>().LastCountryCodeIndex = index;
        }

        /// <summary>
        /// 当输入内容发生变化时触发实时校验。
        /// </summary>
        private void OnInputFieldValueChanged(string newText)
        {
            var result = Validate(newText);
            IsValid = (result == PhoneNumberValidationResult.Valid);
        }

        /// <summary>
        /// 外部可主动调用此方法进行一次强制校验，并获取详细的校验结果
        /// </summary>
        public PhoneNumberValidationResult ForceValidate()
        {
            var result = Validate(UIPhoneNumberInputField.text);
            IsValid = (result == PhoneNumberValidationResult.Valid);
            return result;
        }

        /// <summary>
        /// 核心校验逻辑实现。
        /// </summary>
        private PhoneNumberValidationResult Validate(string phoneNumber)
        {
            // 检查依赖项
            if (UICountryCodeDropdown == null) return PhoneNumberValidationResult.Invalid_DropdownNotSet;
            if (UICountryCodeDropdown.CurrentOption == null)
                return PhoneNumberValidationResult.Invalid_NoOptionSelected;

            // 规则1: 获取限制
            int minLimit = UICountryCodeDropdown.CurrentMinLit;
            int maxLimit = UICountryCodeDropdown.CurrentMaxLit;
            if (phoneNumber.IsNullOrEmpty())
            {
                return PhoneNumberValidationResult.Invalid_Nothing;
            }

            // 检查是否只包含数字
            if (!string.IsNullOrEmpty(phoneNumber) && phoneNumber.Any(c => !char.IsDigit(c)))
            {
                return PhoneNumberValidationResult.Invalid_ContainsNonDigits;
            }

            // 检查长度
            int length = phoneNumber.Length;
            if (length < minLimit) return PhoneNumberValidationResult.Invalid_TooShort;
            if (length > maxLimit) return PhoneNumberValidationResult.Invalid_TooLong;

            // 所有规则通过
            return PhoneNumberValidationResult.Valid;
        }

        public string GetMessageForResult(PhoneNumberValidationResult result)
        {
            switch (result)
            {
                case PhoneNumberValidationResult.Valid:
                    return string.Empty;
                case PhoneNumberValidationResult.Invalid_Nothing:
                    return LocalizationSystem.GetLocalizationContent("EnterPhoneNumber");
                case PhoneNumberValidationResult.Invalid_TooShort:
                    string baseString = LocalizationSystem.GetLocalizationContent("TooShortCharactersPhoneNumber");
                    string finalMessage = string.Format(baseString, UICountryCodeDropdown.CurrentMinLit);
                    return finalMessage;
                case PhoneNumberValidationResult.Invalid_TooLong:
                    string baseString2 = LocalizationSystem.GetLocalizationContent("TooLongCharactersPhoneNumber");
                    string finalMessage2 = string.Format(baseString2, UICountryCodeDropdown.CurrentMaxLit);
                    return finalMessage2;
                case PhoneNumberValidationResult.Invalid_ContainsNonDigits:
                case PhoneNumberValidationResult.Invalid_DropdownNotSet:
                case PhoneNumberValidationResult.Invalid_NoOptionSelected:
                    return LocalizationSystem.GetLocalizationContent("NonexistedPhoneNumber");
                default:
                    return LocalizationSystem.GetLocalizationContent("RequestError");
            }
        }

        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
    }
}
