using System;
using System.Linq;
using System.Text.RegularExpressions;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using QFramework;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace TFG.UI
{
    // 定义验证码的专属校验结果枚举
    public enum VerificationCodeValidationResult
    {
        Valid,
        Error_IsEmpty,
        Error_TooShort,
        E<PERSON>r_TooLong,
        Error_ContainsNonDigits,
        Error_Backend_Incorrect
    }

    public class OnVerificationCodeValidationChanged
    {
        public VerificationCodeValidationResult Result;
        public bool IsValid => Result == VerificationCodeValidationResult.Valid;
    }

    [Serializable]
    public partial class UIVerificationCodeInputComponent : UIComponent, ICanSendEvent, ICanRegisterEvent
    {
        public CanvasGroup CanvasGroup;
        public LayoutElement LayoutElement;
        public TMP_InputField VerificationCodeInputField;
        public string VerificationCode => VerificationCodeInputField?.text;
        private const int MinCharacterCount = 6; //默认最小6
        private const int MaxCharacterCount = 6; //默认最大6
        public bool IsValid { get; private set; } = false;
        private VerificationCodeValidationResult _currentResult;
        private float _originalCaretWidth;
        private LocalizationSystem LocalizationSystem;

        /// <summary>
        /// 初始化，外部设置默认值、并应用表现
        /// </summary>
        public async UniTask InitAndShow()
        {
            await UniTask.Yield();
            if (LocalizationSystem == null)
            {
                LocalizationSystem = this.GetArchitecture().GetSystem<LocalizationSystem>();
            }
            LayoutElement.ignoreLayout = false;
            VerificationCodeInputField.text = string.Empty;
            _originalCaretWidth = VerificationCodeInputField.caretWidth;
            VerificationCodeInputField.onValueChanged.AddListener(OnInputFieldValueChanged);
            VerificationCodeInputField.onSelect.AddListener(ShowLogcat);
            VerificationCodeInputField.onDeselect.AddListener(HideLogcat);
            VerificationCodeInputField.onEndEdit.AddListener(SaveCacheVerificationCode);
            UpdateValidationState(VerificationCodeValidationResult.Error_IsEmpty, true);
            CanvasGroup.EnableView();
        }

        /// <summary>
        /// 初始化，外部重置表现、隐藏输入框
        /// </summary>
        public void HideAndDeinit()
        {
            Clear();
            CanvasGroup.DisableView();
            LayoutElement.ignoreLayout = true;
        }

        private void Clear()
        {
            VerificationCodeInputField.onValueChanged.RemoveListener(OnInputFieldValueChanged);
            VerificationCodeInputField.onSelect.RemoveListener(ShowLogcat);
            VerificationCodeInputField.onDeselect.RemoveListener(HideLogcat);
            VerificationCodeInputField.onEndEdit.RemoveListener(SaveCacheVerificationCode);
            VerificationCodeInputField.text = string.Empty;
            UpdateValidationState(VerificationCodeValidationResult.Error_IsEmpty);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Clear();
        }

        private void SaveCacheVerificationCode(string value)
        {
            LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>().CacheVerifyCode = value;
        }

        private void OnInputFieldValueChanged(string newText)
        {
            var validationResult = Validate(newText);
            UpdateValidationState(validationResult);
        }

        public VerificationCodeValidationResult ForceValidation()
        {
            return Validate(VerificationCodeInputField?.text);
        }
        public void ShowLogcat(string txt)
        {
            VerificationCodeInputField.caretWidth = 1;
            VerificationCodeInputField.MoveTextEnd(false);
        }
        public void HideLogcat(string txt)
        {
            VerificationCodeInputField.caretWidth = (int)0f;
        }

        private VerificationCodeValidationResult Validate(string code)
        {
            // 不能为空
            if (string.IsNullOrEmpty(code))
            {
                return VerificationCodeValidationResult.Error_IsEmpty;
            }

            // 不能含有特殊字符
            if (code.Any(c => !char.IsLetterOrDigit(c)))
            {
                return VerificationCodeValidationResult.Error_ContainsNonDigits;
            }

            // 长度不能短于6个字符
            if (code.Length < MinCharacterCount)
            {
                return VerificationCodeValidationResult.Error_TooShort;
            }

            // 长度不能超过6个字符
            if (code.Length > MaxCharacterCount)
            {
                return VerificationCodeValidationResult.Error_TooLong;
            }

            // 所有客户端规则通过
            return VerificationCodeValidationResult.Valid;
        }

        private void UpdateValidationState(VerificationCodeValidationResult newResult,
            bool forceSendEvent = false)
        {
            if (newResult != _currentResult || forceSendEvent)
            {
                _currentResult = newResult;
                IsValid = (_currentResult == VerificationCodeValidationResult.Valid);
                this.SendEvent(new OnVerificationCodeValidationChanged { Result = _currentResult });
            }
        }

        public string GetMessageForResult(VerificationCodeValidationResult result)
        {
            switch (result)
            {
                case VerificationCodeValidationResult.Valid:
                    return string.Empty;
                case VerificationCodeValidationResult.Error_IsEmpty:
                    return LocalizationSystem.GetLocalizationContent("EnterCode");
                case VerificationCodeValidationResult.Error_TooShort:
                case VerificationCodeValidationResult.Error_TooLong:
                    string finalMessage =LocalizationSystem.GetLocalizationContent("CodeFormatError");
                    return finalMessage;
                case VerificationCodeValidationResult.Error_ContainsNonDigits:
                    return LocalizationSystem.GetLocalizationContent("SpecialCharacters");
                case VerificationCodeValidationResult.Error_Backend_Incorrect:
                    return LocalizationSystem.GetLocalizationContent("IncorrectCode");
                default:
                    return LocalizationSystem.GetLocalizationContent("RequestError");
            }
        }

        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
    }
}
