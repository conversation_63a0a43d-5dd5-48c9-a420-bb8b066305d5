using System;
using System.Collections.Generic;
using System.IO;
using LiteNetLib.Utils;
using MemoryPack;
using TFGShare.Utility;

namespace TFGShare.Protocol
{
    public enum ClientPlatform
    {
        None = 0,
        Windows = 1,
        Mac = 2,
        Ios = 3,
        Android = 4,
        WebGl = 5,
        Editor = 6,
        Linux = 7,
        Other = 100
    }
    //区分选择的游戏模式
    [System.Flags]
    public enum GameSessionType
    {
        None = 0, //异常值
        SinglePlay = 1 << 0, //单人模式
        OnLanPlay = 1 << 1, //局域网对战
        OnLinePlay = 1 << 2, //在线对战
        MatchPlay = 1 << 3 //锦标赛对战
    }
    public enum PlayerRole
    {
        None,
        GameMaster,
        Player,
        Guest,
        Robot
    }

    [MemoryPackable]
    public partial class PlayerData
    {
        public string PlayerId { get; set; }
        public bool IsBanned { get; set; }
        public bool IsTemporary { get; set; }
        public string Username { get; set; }
        public string Role { get; set; }
        public string CharacterId { get; set; }
        public DateTime LastActiveTime { get; set; }
        /// <summary>
        /// 玩家拥有的游戏会话权限。
        /// 使用位掩码存储，默认只开放局域网对战权限。
        /// </summary>
        public GameSessionType AllowedSessionTypes { get; set; } = GameSessionType.OnLanPlay; // 新增字段，并设置默认值

        public override bool Equals(object obj)
        {
            var tmp = obj as PlayerData;
            if (tmp == null) return false;

            if (PlayerId != tmp.PlayerId) return false;
            if (IsBanned != tmp.IsBanned) return false;
            if (IsTemporary != tmp.IsTemporary) return false;
            if (Username != tmp.Username) return false;
            if (CharacterId != tmp.CharacterId) return false;
            if (AllowedSessionTypes != tmp.AllowedSessionTypes) return false;
            return true;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(PlayerId, Username,IsBanned,IsTemporary, CharacterId, LastActiveTime, AllowedSessionTypes);
        }

        public PlayerData GetACopy()
        {
            return (PlayerData)this.MemberwiseClone();
        }
        /// <returns>如果拥有对应权限则返回true</returns>
        public bool HasPermission(GameSessionType sessionType)
        {
            return (AllowedSessionTypes & sessionType) == sessionType;
        }
        /// <param name="sessionType">要授予的权限</param>
        public void GrantPermission(GameSessionType sessionType)
        {
            AllowedSessionTypes |= sessionType;
        }
        /// <param name="sessionType">要撤销的权限</param>
        public void RevokePermission(GameSessionType sessionType)
        {
            // 使用位与和位非操作移除标志位
            AllowedSessionTypes &= ~sessionType;
        }

        //将玩家拥有的权限作为列表返回
        public List<GameSessionType> GameSessionTypeList()
        {
            var res = new List<GameSessionType>();
            foreach (GameSessionType flag in Enum.GetValues(typeof(GameSessionType)))
            {
                int flagValue = (int)flag;
                // 过滤掉 None(0) 和复合值
                if (flagValue == 0 || (flagValue & (flagValue - 1)) != 0)
                {
                    continue;
                }

                if (this.AllowedSessionTypes.HasFlag(flag))
                {
                    res.Add(flag);
                }
            }

            return res;
        }

        /// <summary>
        /// 通过一个权限列表来设置玩家拥有的所有会话权限，此操作会覆盖之前的设置。
        /// </summary>
        /// <param name="gameSessionTypes">要设置的权限列表。</param>
        public void SetAllowedSessionTypesByList(IEnumerable<GameSessionType> gameSessionTypes)
        {
            AllowedSessionTypes = GameSessionType.None;
            if (gameSessionTypes == null)
            {
                return;
            }
            foreach (var permission in gameSessionTypes)
            {
                GrantPermission(permission);
            }
        }
    }

    public class RoomPlayer : INetSerializable
    {
        public string UserId { get; set; }
        public string Nickname { get; set; }
        public bool IsReady { get; set; }

        public void Serialize(NetDataWriter writer)
        {
            writer.Put(UserId);
            writer.Put(Nickname);
            writer.Put(IsReady);
        }

        public void Deserialize(NetDataReader reader)
        {
            UserId = reader.GetString();
            Nickname = reader.GetString();
            IsReady = reader.GetBool();
        }
    }
}
